package yogu.pro.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 后端统一返回结果
 * @param <T>
 */
@Data
public class Result<T> implements Serializable {

    private Integer code; // 编码：200成功，0和其它数字为失败
    private String msg; // 返回信息
    private T data; // 数据

    // 成功，无数据
    public static <T> Result<T> success() {
        Result<T> result = new Result<T>();
        result.code = 200;
        result.msg = "成功"; // 默认成功信息
        return result;
    }

    // 成功，有数据
    public static <T> Result<T> success(T object) {
        Result<T> result = new Result<T>();
        result.data = object;
        result.code = 200;
        result.msg = "成功"; // 默认成功信息
        return result;
    }

    // 成功，有数据，带消息
    public static <T> Result<T> success(T object, String msg) {
        Result<T> result = new Result<T>();
        result.data = object;
        result.code = 200;
        result.msg = msg; // 自定义成功信息
        return result;
    }

    // 成功，无数据，带消息
    public static <T> Result<T> successMsg(String msg) {
        Result<T> result = new Result<T>();
        result.code = 200;
        result.msg = msg; // 自定义成功信息
        return result;
    }

    // 错误
    public static <T> Result<T> error(String msg) {
        Result<T> result = new Result<T>();
        result.msg = msg;
        result.code = 0;
        return result;
    }
}
