package yogu.pro.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 阿里云OSS配置属性
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssProperties {
    
    /**
     * OSS端点
     */
    private String endpoint;
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 访问密钥
     */
    private String accessKeySecret;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 访问域名
     */
    private String domain;
    
    /**
     * 文件上传路径前缀
     */
    private String prefix = "lottery-system/";
    
    /**
     * 允许上传的文件扩展名
     */
    private List<String> allowedExtensions;
    
    /**
     * 最大文件大小（MB）
     */
    private Integer maxFileSize = 5;
} 