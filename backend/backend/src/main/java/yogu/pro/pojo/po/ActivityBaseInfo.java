package yogu.pro.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖活动基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_base_info")
public class ActivityBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动记录唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动类型 （1-扫码抽奖）
     */
    @TableField(value = "activity_type")
    private Integer activityType;

    /**
     * 活动形式 （1-转盘 2-九宫格）
     */
    @TableField(value = "activity_format")
    private Integer activityFormat;

    /**
     * 活动名称
     */
    @TableField(value = "activity_name")
    private String activityName;

    /**
     * 活动开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 活动规则
     */
    @TableField(value = "activity_rule")
    private String activityRule;

    /**
     * 隐私条款
     */
    @TableField(value = "privacy_policy")
    private String privacyPolicy;

    /**
     * 创建人（如用户账号/系统标识）
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否启用 1-启用 0-停用
     */
    @TableField(value = "is_enabled")
    private Integer isEnabled;


}
