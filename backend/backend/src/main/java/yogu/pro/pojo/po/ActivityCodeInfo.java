package yogu.pro.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖活动二维码信息表（无自增ID，code_value作为唯一标识）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_code_info")
public class ActivityCodeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 二维码对应的code值，作为唯一标识
     */
    @TableId(value = "code_value")
    private String codeValue;

    /**
     * 关联的活动ID，对应activity_base_info表的id
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 是否已核销，0-未核销，1-已核销
     */
    @TableField(value = "is_redeemed")
    private Integer isRedeemed;

    /**
     * 绑定该code的手机号，可为空
     */
    @TableField(value = "bound_phone_number")
    private String boundPhoneNumber;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 核销时间，已核销时不为空
     */
    @TableField(value = "redeem_time")
    private LocalDateTime redeemTime;


}
