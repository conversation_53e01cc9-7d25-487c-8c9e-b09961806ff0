package yogu.pro.pojo.po;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖活动奖品及概率配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_prize_prob")
public class ActivityPrizeProb implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联活动基础信息表ID
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 奖品类型（1-奖品、2-红包、3-谢谢参与 ）
     */
    @TableField(value = "prize_type")
    private Integer prizeType;

    /**
     * 奖品名称
     */
    @TableField(value = "prize_name")
    private String prizeName;

    /**
     * 奖品数量
     */
    @TableField(value = "prize_stock")
    private Integer prizeStock;

    /**
     * 红包金额
     */
    @TableField(value = "red_envelope_amount")
    private BigDecimal redEnvelopeAmount;

    /**
     * 中奖概率（如 20.00 代表20% ）
     */
    @TableField(value = "win_prob")
    private BigDecimal winProb;

    /**
     * 图片(OSS地址)
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;


}
