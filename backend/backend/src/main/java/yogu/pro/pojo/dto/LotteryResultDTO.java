package yogu.pro.pojo.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 抽奖结果DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class LotteryResultDTO {
    
    /**
     * 是否中奖
     */
    private Boolean isWon;
    
    /**
     * 奖品ID
     */
    private Long prizeId;
    
    /**
     * 奖品名称
     */
    private String prizeName;
    
    /**
     * 奖品类型（1-奖品、2-红包、3-谢谢参与）
     */
    private Integer prizeType;
    
    /**
     * 红包金额（仅红包类型有效）
     */
    private BigDecimal redEnvelopeAmount;
    
    /**
     * 奖品图片
     */
    private String prizeIcon;
    
    /**
     * 中奖概率
     */
    private BigDecimal winProb;
    
    /**
     * 活动ID
     */
    private Long activityId;
} 