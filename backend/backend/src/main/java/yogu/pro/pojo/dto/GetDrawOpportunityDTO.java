package yogu.pro.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 校验资质并增加抽奖次数请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class GetDrawOpportunityDTO {
    
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phoneNumber;
    
    /**
     * 二维码验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;
    
    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;
} 