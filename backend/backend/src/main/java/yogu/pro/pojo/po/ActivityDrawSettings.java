package yogu.pro.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖活动规则设置表（含积分、次数、城市等限制）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_draw_settings")
public class ActivityDrawSettings implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联活动主表ID
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 是否需要消耗积分 1-是 0-否
     */
    @TableField(value = "need_consume_point")
    private Integer needConsumePoint;

    /**
     * 每次抽奖消耗的积分（need_consume_point=1时必填）
     */
    @TableField(value = "consume_point_num")
    private Integer consumePointNum;

    /**
     * 每人每天最多抽奖次数
     */
    @TableField(value = "daily_limit_count")
    private Integer dailyLimitCount;

    /**
     * 每人每活动最多抽奖次数
     */
    @TableField(value = "activity_limit_count")
    private Integer activityLimitCount;

    /**
     * 中奖次数限制类型 1-无限制 2-最多N次
     */
    @TableField(value = "win_limit_type")
    private Integer winLimitType;

    /**
     * 每人最多抽中次数（win_limit_type=2时必填）
     */
    @TableField(value = "max_win_count")
    private Integer maxWinCount;

    /**
     * 参与城市编码列表，如 ["BJ", "SH", "GZ"]
     */
    @TableField(value = "participate_cities")
    private String participateCities;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;


}
