package yogu.pro.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_info")
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户手机号，作为用户名使用，唯一
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 微信用户唯一标识，用于微信对接
     */
    @TableField(value = "openid")
    private String openid;

    /**
     * 用户创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 用户最后登录时间，可为空，首次登录前无记录
     */
    @TableField(value = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 用户账号是否激活，1-激活，0-未激活
     */
    @TableField(value = "is_active")
    private Integer isActive;


}
