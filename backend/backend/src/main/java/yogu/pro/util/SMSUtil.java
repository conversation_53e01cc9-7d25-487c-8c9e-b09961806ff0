package yogu.pro.util;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 短信工具类
 */
public class SMSUtil {

    /**
     * 发送短信
     *
     * @param signName 签名
     * @param templateCode 模板
     * @param phoneNumbers 手机号
     * @param param 参数
     */
    public static void sendMessage(String signName, String templateCode, String phoneNumbers, String param) {
        /*
                这三个需要自己填写
                cn-hangzhou：服务地区，选择距离自己近的，我选择的是cn-hangzhou
                AccessKey ID：访问阿里云 API 的Id
                AccessKey Secret：Id对应的密钥，具有该账户相应的权限
         */
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou",
                "LTAI5tAzkeAaJpnUGpXxHjch",
                "******************************");
        IAcsClient client = new DefaultAcsClient(profile);

        SendSmsRequest request = new SendSmsRequest();
        request.setSysRegionId("cn-hangzhou");
        request.setPhoneNumbers(phoneNumbers);
        request.setSignName(signName);
        request.setTemplateCode(templateCode);
        request.setTemplateParam("{\"code\":\"" + param + "\"}");
        try {
            SendSmsResponse response = client.getAcsResponse(request);
            System.out.println(response.getMessage());
            System.out.println(response.getBizId());
            System.out.println(response.getCode());
            System.out.println(response.getRequestId());
        } catch (ClientException e) {
            e.printStackTrace();
        }
    }
}
