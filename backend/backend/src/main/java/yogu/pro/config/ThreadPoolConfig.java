package yogu.pro.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 全局线程池
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {

    private static final int cpuCount = Runtime.getRuntime().availableProcessors();

    private static final int corePoolSize = cpuCount * 2;

    private static final int maxPoolSize = cpuCount * 4;

    private static final int queueCapacity = 30;

    private static final int keepAlive = 5000;

    @Bean(name = "globalThreadPool")
    public ThreadPoolTaskExecutor globalThreadPool(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
        threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
        threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
        threadPoolTaskExecutor.setKeepAliveSeconds(keepAlive);
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        //线程名字前缀
        threadPoolTaskExecutor.setThreadNamePrefix("global-");
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean(name = "threadPoolExecutor")
    public ExecutorService threadPoolExecutor(){
        return new ThreadPoolExecutor(
                cpuCount,
                maxPoolSize,
                keepAlive,
                TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1000),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "taskScheduler")
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(6);
        return taskScheduler;
    }

}
