package yogu.pro.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfiguration {
    @Bean
    public OpenAPI smileQiOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("可口可乐抽奖")
                        .description("可口可乐抽奖接口文档")
                        .version("v1.0")
                        .license(new License().name("Apache 2.0"))
                        .contact(new Contact().name("mcayan66").email("<EMAIL>")))
                .externalDocs(new ExternalDocumentation()
                        .description("外部文档")
                        .url("https://springshop.wiki.github.org/docs"));
    }


    // 接口较多，可以进行分组，此处进行注释
/*    @Bean
    public GroupedOpenApi backendGroup() {
        return GroupedOpenApi.builder().group("user").displayName("user")
                .addOpenApiCustomizer(openApi -> openApi.info(new Info().title("UJCMS 后台 API").version("1.0.0")))
                .packagesToScan("com.smileqi.web.controller.user")
                .pathsToMatch("/**")
                .build();
    }*/
}