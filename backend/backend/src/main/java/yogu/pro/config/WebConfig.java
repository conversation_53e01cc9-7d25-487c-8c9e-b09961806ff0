package yogu.pro.config;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import yogu.pro.interceptors.LoginInterceptor;
import yogu.pro.interceptors.ApiSignatureInterceptor;

/**
 * <AUTHOR>
 * @date ：2024/4/12 下午9:18
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final LoginInterceptor loginInterceptor;
    private final ApiSignatureInterceptor apiSignatureInterceptor;

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setTaskExecutor(taskExecutor());
        // 设置超时时间
        configurer.setDefaultTimeout(30000);
    }

    @Bean
    public AsyncTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(100);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("mvc-async-");
        executor.initialize();
        return executor;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")  //指定允许跨域请求的路径模式为"/**"，表示所有的路径都将允许跨域访问。
                .allowedOrigins("*") // 允许访问资源的域名
                .allowedMethods("*") // 允许的HTTP方法
                .allowedHeaders("*") // 允许的请求头
                .allowCredentials(false) // 是否允许发送凭证信息
                .maxAge(3600); // 预检请求的有效期
    }

    //注册登录token验证拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginInterceptor).excludePathPatterns(
                "/v3/**",
                "/webjars/**",
                "/doc.html");
        
        //注册API签名验证拦截器
        registry.addInterceptor(apiSignatureInterceptor)
                .addPathPatterns(
                "/api/lotteryDraw");
    }



}
