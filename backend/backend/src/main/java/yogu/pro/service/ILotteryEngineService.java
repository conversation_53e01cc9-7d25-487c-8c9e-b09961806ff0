package yogu.pro.service;

import yogu.pro.pojo.po.ActivityPrizeProb;

/**
 * 抽奖引擎服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ILotteryEngineService {
    
    /**
     * 执行抽奖
     * 
     * @param activityId 活动ID
     * @return 中奖奖品信息
     */
    ActivityPrizeProb performLottery(Long activityId);
    
    /**
     * 根据正态分布生成随机数
     * 
     * @param mean 均值
     * @param standardDeviation 标准差
     * @return 随机数
     */
    double generateNormalDistribution(double mean, double standardDeviation);
    
    /**
     * 根据概率权重选择奖品
     * 
     * @param prizes 奖品列表
     * @return 选中的奖品
     */
    ActivityPrizeProb selectPrizeByWeight(java.util.List<ActivityPrizeProb> prizes);
} 