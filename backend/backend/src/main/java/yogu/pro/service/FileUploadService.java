package yogu.pro.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件上传服务接口
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface FileUploadService {
    
    /**
     * 上传图片到OSS
     * 
     * @param file 图片文件
     * @param module 模块名称（如: brand, activity, prize）
     * @return 上传结果，包含文件URL等信息
     */
    Map<String, Object> uploadImage(MultipartFile file, String module);
    
    /**
     * 验证文件是否为有效的图片
     * 
     * @param file 文件
     * @return 是否为有效图片
     */
    boolean isValidImage(MultipartFile file);
    
    /**
     * 生成唯一文件名
     * 
     * @param originalFileName 原始文件名
     * @param module 模块名称
     * @return 唯一文件名
     */
    String generateUniqueFileName(String originalFileName, String module);
    
    /**
     * 删除OSS文件
     * 
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);
} 