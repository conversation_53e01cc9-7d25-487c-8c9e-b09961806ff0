package yogu.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yogu.pro.pojo.dto.DrawOpportunityResponseDTO;
import yogu.pro.pojo.dto.LotteryResultDTO;
import yogu.pro.pojo.po.*;
import yogu.pro.result.Result;
import yogu.pro.service.*;

import java.time.LocalDateTime;
import yogu.pro.util.DateUtil;

/**
 * 抽奖服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LotteryDrawServiceImpl implements ILotteryDrawService {
    
    private final IActivityBaseInfoService activityBaseInfoService;
    private final IActivityDrawSettingsService activityDrawSettingsService;
    private final IActivityCodeInfoService activityCodeInfoService;
    private final IUserDrawCountService userDrawCountService;
    private final ICodeVerificationService codeVerificationService;
    private final ILotteryEngineService lotteryEngineService;
    private final IUserBlacklistService userBlacklistService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<DrawOpportunityResponseDTO> getDrawOpportunity(String phoneNumber, String code, Long activityId) {
        try {
            // 1. 黑名单用户校验
            LambdaQueryWrapper<UserBlacklist> blacklistQuery = new LambdaQueryWrapper<>();
            blacklistQuery.eq(UserBlacklist::getPhoneNumber, phoneNumber);
            UserBlacklist blacklistUser = userBlacklistService.getOne(blacklistQuery);
            
            if (blacklistUser != null) {
                return Result.error("该用户已被加入黑名单，无法参与活动");
            }

            // 2. 根据活动ID查询该活动是否在活动期内
            ActivityBaseInfo activity = activityBaseInfoService.getById(activityId);
            if (activity == null) {
                return Result.error("活动不存在");
            }
            
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(activity.getStartTime()) || now.isAfter(activity.getEndTime())) {
                return Result.error("活动不在进行中");
            }
            
            if (activity.getIsEnabled() != 1) {
                return Result.error("活动已停用");
            }
            
            // 3. 查询活动抽奖设置
            LambdaQueryWrapper<ActivityDrawSettings> settingsQuery = new LambdaQueryWrapper<>();
            settingsQuery.eq(ActivityDrawSettings::getActivityId, activityId);
            ActivityDrawSettings drawSettings = activityDrawSettingsService.getOne(settingsQuery);
            
            if (drawSettings == null) {
                return Result.error("活动抽奖设置不存在");
            }
            
            // 4. 检查每日扫码次数限制
            String today = DateUtil.getCurrentDateString();
            LambdaQueryWrapper<ActivityCodeInfo> dailyQuery = new LambdaQueryWrapper<>();
            dailyQuery.eq(ActivityCodeInfo::getBoundPhoneNumber, phoneNumber)
                     .eq(ActivityCodeInfo::getActivityId, activityId)
                     .eq(ActivityCodeInfo::getIsRedeemed, 1)
                     .apply("DATE(create_time) = {0}", today);
            
            long dailyCount = activityCodeInfoService.count(dailyQuery);
            if (drawSettings.getDailyLimitCount() != null && dailyCount >= drawSettings.getDailyLimitCount()) {
                return Result.error("今日扫码次数已达上限");
            }
            
            // 5. 检查活动总扫码次数限制
            LambdaQueryWrapper<ActivityCodeInfo> totalQuery = new LambdaQueryWrapper<>();
            totalQuery.eq(ActivityCodeInfo::getBoundPhoneNumber, phoneNumber)
                     .eq(ActivityCodeInfo::getActivityId, activityId)
                     .eq(ActivityCodeInfo::getIsRedeemed, 1);
            
            long totalCount = activityCodeInfoService.count(totalQuery);
            if (drawSettings.getActivityLimitCount() != null && totalCount >= drawSettings.getActivityLimitCount()) {
                return Result.error("活动扫码次数已达上限");
            }
            
            // 6. 查询code值是否已存在
            LambdaQueryWrapper<ActivityCodeInfo> codeQuery = new LambdaQueryWrapper<>();
            codeQuery.eq(ActivityCodeInfo::getCodeValue, code);
            ActivityCodeInfo existingCode = activityCodeInfoService.getOne(codeQuery);
            
            if (existingCode != null) {
                return Result.error("该二维码已被使用");
            }
            
            // 6. 保存code到数据库
            ActivityCodeInfo newCode = new ActivityCodeInfo();
            newCode.setCodeValue(code);
            newCode.setActivityId(activityId);
            // 未核验
            newCode.setIsRedeemed(0);
            newCode.setBoundPhoneNumber(phoneNumber);
            newCode.setCreateTime(LocalDateTime.now());
            activityCodeInfoService.save(newCode);
            
            // 7. 调用第三方API校验code真伪
            boolean isValid = codeVerificationService.verifyCode(code);
            if (!isValid) {
                return Result.error("二维码验证失败");
            }
            
            // 8. 验证成功，将code状态改为已核验
            newCode.setIsRedeemed(1);
            newCode.setRedeemTime(LocalDateTime.now());
            activityCodeInfoService.updateById(newCode);
            
            // 9. 查询或创建用户抽奖次数记录
            LambdaQueryWrapper<UserDrawCount> drawCountQuery = new LambdaQueryWrapper<>();
            drawCountQuery.eq(UserDrawCount::getPhoneNumber, phoneNumber)
                         .eq(UserDrawCount::getActivityId, activityId);
            UserDrawCount userDrawCount = userDrawCountService.getOne(drawCountQuery);
            
            if (userDrawCount == null) {
                // 创建新记录
                userDrawCount = new UserDrawCount();
                userDrawCount.setPhoneNumber(phoneNumber);
                userDrawCount.setActivityId(activityId);
                userDrawCount.setDrawCount(0);
                //走到此处是第一次核验是有抽奖机会的
                userDrawCount.setRemainingDrawCount(1);
                userDrawCount.setCreateTime(LocalDateTime.now());
                userDrawCount.setUpdateTime(LocalDateTime.now());
                userDrawCountService.save(userDrawCount);
            } else {
                // 更新抽奖次数
                userDrawCount.setRemainingDrawCount(userDrawCount.getRemainingDrawCount() + 1);
                userDrawCount.setUpdateTime(LocalDateTime.now());
                userDrawCountService.updateById(userDrawCount);
            }
            
            log.info("用户 {} 在活动 {} 中校验资质成功，剩余抽奖次数: {}", 
                    phoneNumber, activityId, userDrawCount.getRemainingDrawCount());
            
            // 构建响应数据
            DrawOpportunityResponseDTO response = new DrawOpportunityResponseDTO();
            response.setPhoneNumber(phoneNumber);
            response.setActivityId(activityId);
            response.setRemainingDrawCount(userDrawCount.getRemainingDrawCount());
            response.setDrawCount(userDrawCount.getDrawCount());
            
            return Result.success(response, "校验资质成功，抽奖次数已增加");
            
        } catch (Exception e) {
            log.error("获取抽奖机会异常: {}", e.getMessage(), e);
            return Result.error("系统异常，请稍后重试");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<LotteryResultDTO> performDraw(String phoneNumber, Long activityId) {
        try {
            // 1. 黑名单用户校验
            LambdaQueryWrapper<UserBlacklist> blacklistQuery = new LambdaQueryWrapper<>();
            blacklistQuery.eq(UserBlacklist::getPhoneNumber, phoneNumber);
            UserBlacklist blacklistUser = userBlacklistService.getOne(blacklistQuery);
            
            if (blacklistUser != null) {
                return Result.error("该用户已被加入黑名单，无法参与抽奖");
            }
            
            // 2. 检查用户抽奖次数
            LambdaQueryWrapper<UserDrawCount> drawCountQuery = new LambdaQueryWrapper<>();
            drawCountQuery.eq(UserDrawCount::getPhoneNumber, phoneNumber)
                         .eq(UserDrawCount::getActivityId, activityId);
            UserDrawCount userDrawCount = userDrawCountService.getOne(drawCountQuery);
            
            if (userDrawCount == null || userDrawCount.getRemainingDrawCount() <= 0) {
                return Result.error("抽奖次数不足");
            }
            
            // 3. 调用抽奖引擎执行抽奖
            ActivityPrizeProb selectedPrize = lotteryEngineService.performLottery(activityId);
            
            if (selectedPrize == null) {
                return Result.error("抽奖失败，请稍后重试");
            }
            
            // 4. 消耗抽奖次数
            userDrawCount.setRemainingDrawCount(userDrawCount.getRemainingDrawCount() - 1);
            userDrawCount.setDrawCount(userDrawCount.getDrawCount() + 1);
            userDrawCount.setUpdateTime(LocalDateTime.now());
            userDrawCountService.updateById(userDrawCount);
            
            // 5. 构建抽奖结果
            LotteryResultDTO result = new LotteryResultDTO();
            result.setIsWon(true);
            result.setPrizeId(selectedPrize.getId());
            result.setPrizeName(selectedPrize.getPrizeName());
            result.setPrizeType(selectedPrize.getPrizeType());
            result.setRedEnvelopeAmount(selectedPrize.getRedEnvelopeAmount());
            result.setPrizeIcon(selectedPrize.getIcon());
            result.setWinProb(selectedPrize.getWinProb());
            result.setActivityId(activityId);
            
            log.info("用户 {} 在活动 {} 中抽奖成功，奖品: {}, 剩余次数: {}", 
                    phoneNumber, activityId, selectedPrize.getPrizeName(), 
                    userDrawCount.getRemainingDrawCount());
            
            return Result.success(result, "抽奖成功");
            
        } catch (Exception e) {
            log.error("抽奖异常: {}", e.getMessage(), e);
            return Result.error("抽奖失败，请稍后重试");
        }
    }
} 