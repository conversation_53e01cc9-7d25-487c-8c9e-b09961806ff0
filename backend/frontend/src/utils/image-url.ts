/**
 * 图片URL处理工具
 */

// 阿里云OSS配置
const OSS_CONFIG = {
  bucket: 'common-prize',
  region: 'cn-beijing',
  baseUrl: 'https://common-prize.oss-cn-beijing.aliyuncs.com'
}

/**
 * 将示例URL转换为阿里云OSS URL
 * @param url 原始图片URL
 * @returns 转换后的OSS URL
 */
export const convertToOSSUrl = (url: string): string => {
  if (!url) return ''
  
  // 如果已经是OSS URL，直接返回
  if (url.includes('oss-cn-beijing.aliyuncs.com')) {
    return url
  }
  
  // 如果是example.com的URL，转换为OSS URL
  if (url.includes('example.com/logo/')) {
    const fileName = url.split('/').pop() || 'default.png'
    return `${OSS_CONFIG.baseUrl}/brand-logos/${fileName}`
  }
  
  // 如果是via.placeholder.com的URL，转换为OSS URL
  if (url.includes('via.placeholder.com')) {
    const match = url.match(/text=([A-Z])/)
    const letter = match ? match[1] : 'DEFAULT'
    return `${OSS_CONFIG.baseUrl}/brand-logos/brand_${letter.toLowerCase()}.png`
  }
  
  // 如果是本地blob URL，直接返回
  if (url.startsWith('blob:')) {
    return url
  }
  
  // 其他情况，尝试构建OSS URL
  const fileName = url.split('/').pop() || 'default.png'
  return `${OSS_CONFIG.baseUrl}/brand-logos/${fileName}`
}

/**
 * 验证是否为有效的图片URL
 * @param url 图片URL
 * @returns 是否有效
 */
export const isValidImageUrl = (url: string): boolean => {
  if (!url) return false
  
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
  const lowerUrl = url.toLowerCase()
  
  return imageExtensions.some(ext => lowerUrl.includes(ext)) || 
         url.startsWith('blob:') || 
         url.includes('placeholder')
}

/**
 * 获取图片的显示URL（用于前端显示）
 * @param url 原始URL
 * @returns 显示用的URL
 */
export const getDisplayImageUrl = (url: string): string => {
  if (!url) return ''
  
  // 如果是本地blob URL，直接返回
  if (url.startsWith('blob:')) {
    return url
  }
  
  // 转换为OSS URL
  return convertToOSSUrl(url)
}

/**
 * 生成默认品牌Logo URL
 * @param brandName 品牌名称
 * @returns 默认Logo URL
 */
export const getDefaultBrandLogoUrl = (brandName: string): string => {
  const firstChar = brandName.charAt(0).toUpperCase()
  return `${OSS_CONFIG.baseUrl}/brand-logos/default_${firstChar}.png`
} 