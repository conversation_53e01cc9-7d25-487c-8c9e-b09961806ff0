import axios from 'axios'
import type { AxiosResponse, AxiosError, AxiosInstance } from 'axios'
import { ElMessage } from 'element-plus'

// API响应数据类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 自定义请求实例类型
interface CustomAxiosInstance extends Omit<AxiosInstance, 'get' | 'post' | 'put' | 'delete'> {
  get<T = any>(url: string, config?: any): Promise<T>
  post<T = any>(url: string, data?: any, config?: any): Promise<T>
  put<T = any>(url: string, data?: any, config?: any): Promise<T>
  delete<T = any>(url: string, config?: any): Promise<T>
}

// 创建axios实例
const request = axios.create({
  baseURL: '/api', // 使用Vite代理，避免CORS问题
  timeout: 30000, // 请求超时时间增加到30秒，避免频繁超时
}) as CustomAxiosInstance

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 可以在这里添加token等认证信息
    
    // 详细日志记录，用于调试
    console.log('发送API请求:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      data: config.data,
      params: config.params
    })
    
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做点什么
    console.log('✅ HTTP响应成功:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    })
    
    const responseData = response.data as ApiResponse
    
    // 检查API返回的code字段
    if (responseData.code !== undefined && responseData.code !== 200) {
      // 非200都是错误，抛出异常并显示系统错误
      const errorMessage = responseData.message || `系统错误(${responseData.code})`
      console.error('❌ API业务错误:', {
        url: response.config.url,
        code: responseData.code,
        message: responseData.message,
        data: responseData.data
      })
      
      // 显示错误弹窗
      ElMessage.error(`系统错误: ${errorMessage}`)
      
      // 抛出异常
      const error = new Error(errorMessage)
      ;(error as any).code = responseData.code
      ;(error as any).data = responseData.data
      return Promise.reject(error)
    }
    
    // code为200或未定义时，返回原始响应数据
    return response.data
  },
  (error: AxiosError) => {
    // 对响应错误做点什么
    console.error('❌ HTTP请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      code: error.code
    })
    
    let message = '网络错误，请重试'
    let shouldShowMessage = true
    
    if (error.response) {
      // 服务器返回了错误状态码
      const errorData = error.response.data as any
      if (errorData && errorData.message) {
        message = `系统错误: ${errorData.message}`
      } else {
        switch (error.response.status) {
          case 400:
            message = '请求参数错误'
            if (errorData && errorData.error) {
              message += `: ${errorData.error}`
            }
            break
          case 401:
            message = '未授权，请重新登录'
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = '请求资源不存在'
            break
          case 500:
            message = '服务器内部错误'
            break
          default:
            message = `连接错误${error.response.status}`
        }
      }
    } else if (error.request) {
      // 网络连接错误
      if (error.code === 'ECONNREFUSED' || error.message?.includes('ECONNREFUSED')) {
        message = '后端服务不可用，请确保后端服务已启动'
        console.warn('🚨 后端服务连接失败，请检查服务状态')
      } else if (error.code === 'ECONNRESET' || error.message?.includes('ECONNRESET')) {
        message = '连接被重置，请重试'
        shouldShowMessage = false // 连接重置时不显示错误提示
      } else {
        message = '网络连接异常，请检查网络连接'
      }
    }
    
    // 对于奖品管理相关的API请求，添加特殊处理
    if (error.config?.url?.includes('/prize')) {
      console.error('🎁 奖品API请求失败:', {
        url: error.config.url,
        errorType: error.code || 'unknown',
        details: error.message
      })
    }
    
    // 对于活动管理相关的API请求，添加特殊处理
    if (error.config?.url?.includes('/activity')) {
      console.error('🎯 活动API请求失败:', {
        url: error.config.url,
        errorType: error.code || 'unknown',
        details: error.message,
        isTimeout: error.code === 'ECONNABORTED' || error.message?.includes('timeout')
      })
      
      // 对于超时错误，提供更友好的提示
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        message = '活动数据加载超时，请检查网络连接或稍后重试'
        console.warn('⏱️ 活动API请求超时，可能是网络延迟或服务器响应慢')
      }
    }
    
    if (shouldShowMessage) {
      ElMessage.error(message)
    }
    return Promise.reject(error)
  }
)

export default request 