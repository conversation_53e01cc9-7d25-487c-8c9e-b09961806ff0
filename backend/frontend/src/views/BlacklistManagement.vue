<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">黑名单管理</h1>
      <p class="text-gray-600">管理系统黑名单，防止恶意用户参与抽奖</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
          <input
            v-model="filters.phone"
            type="text"
            placeholder="搜索手机号"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">人员类型</label>
          <select
            v-model="filters.personType"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部</option>
            <option value="恶意用户">恶意用户</option>
            <option value="刷奖用户">刷奖用户</option>
            <option value="违规用户">违规用户</option>
            <option value="系统黑名单">系统黑名单</option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="searchBlacklist"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            搜索
          </button>
        </div>
        <div class="flex items-end">
          <button
            @click="clearFilters"
            class="w-full bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
          >
            重置
          </button>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="showAddModal = true"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加黑名单
        </button>
        <button
          @click="showImportModal = true"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <Upload class="w-4 h-4 mr-2" />
          批量导入
        </button>
        <button
          @click="exportBlacklist"
          class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center"
        >
          <Download class="w-4 h-4 mr-2" />
          导出数据
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总黑名单数</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalCount }}</p>
          </div>
          <div class="p-3 bg-red-100 rounded-full">
            <Shield class="w-6 h-6 text-red-600" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">本月新增</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.monthlyNew }}</p>
          </div>
          <div class="p-3 bg-orange-100 rounded-full">
            <TrendingUp class="w-6 h-6 text-orange-600" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">恶意用户</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.maliciousUsers }}</p>
          </div>
          <div class="p-3 bg-yellow-100 rounded-full">
            <AlertTriangle class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
      </div>
    </div>

    <!-- 黑名单列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">黑名单列表</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                手机号
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                人员类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                添加原因
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                添加时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in blacklist" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <Phone class="w-4 h-4 text-red-600" />
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ item.phone }}</div>
                    <div class="text-sm text-gray-500">ID: {{ item.id }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="{
                    'bg-red-100 text-red-800': item.personType === '恶意用户',
                    'bg-orange-100 text-orange-800': item.personType === '刷奖用户',
                    'bg-yellow-100 text-yellow-800': item.personType === '违规用户',
                    'bg-gray-100 text-gray-800': item.personType === '系统黑名单'
                  }"
                >
                  {{ item.personType }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.reason }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(item.addTime) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="{
                    'bg-red-100 text-red-800': item.status === '生效中',
                    'bg-green-100 text-green-800': item.status === '已解除'
                  }"
                >
                  {{ item.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="viewDetail(item)"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  查看详情
                </button>
                <button
                  v-if="item.status === '生效中'"
                  @click="removeFromBlacklist(item)"
                  class="text-green-600 hover:text-green-900"
                >
                  解除黑名单
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ pagination.start }} 到 {{ pagination.end }} 条，共 {{ pagination.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="prevPage"
              :disabled="pagination.currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="px-3 py-1 text-sm text-gray-700">
              {{ pagination.currentPage }} / {{ pagination.totalPages }}
            </span>
            <button
              @click="nextPage"
              :disabled="pagination.currentPage === pagination.totalPages"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加黑名单模态框 -->
    <div v-if="showAddModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="showAddModal = false">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">添加黑名单</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                <input
                  v-model="newItem.phone"
                  type="text"
                  placeholder="请输入手机号"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">人员类型</label>
                <select
                  v-model="newItem.personType"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">请选择人员类型</option>
                  <option value="恶意用户">恶意用户</option>
                  <option value="刷奖用户">刷奖用户</option>
                  <option value="违规用户">违规用户</option>
                  <option value="系统黑名单">系统黑名单</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">添加原因</label>
                <textarea
                  v-model="newItem.reason"
                  rows="3"
                  placeholder="请输入添加原因"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="addToBlacklist"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              添加
            </button>
            <button
              @click="showAddModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 导入模态框 -->
    <div v-if="showImportModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="showImportModal = false">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">批量导入黑名单</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  @change="handleFileUpload"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                <p>支持格式：Excel (.xlsx, .xls) 或 CSV 文件</p>
                <p>文件应包含：手机号、人员类型、添加原因 三列</p>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="importBlacklist"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              导入
            </button>
            <button
              @click="showImportModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  Shield, TrendingUp, AlertTriangle, Phone, Plus, Upload, Download 
} from 'lucide-vue-next'

// 类型定义
interface BlacklistItem {
  id: number
  phone: string
  personType: string
  reason: string
  addTime: string
  status: '生效中' | '已解除'
}

interface Filters {
  phone: string
  personType: string
}

interface Stats {
  totalCount: number
  monthlyNew: number
  maliciousUsers: number
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  start: number
  end: number
}

interface NewItem {
  phone: string
  personType: string
  reason: string
}

// 响应式数据
const blacklist = ref<BlacklistItem[]>([])
const filters = ref<Filters>({
  phone: '',
  personType: ''
})
const stats = ref<Stats>({
  totalCount: 0,
  monthlyNew: 0,
  maliciousUsers: 0
})
const pagination = ref<Pagination>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  start: 0,
  end: 0
})

const showAddModal = ref(false)
const showImportModal = ref(false)
const newItem = ref<NewItem>({
  phone: '',
  personType: '',
  reason: ''
})

// 模拟数据
const mockBlacklist: BlacklistItem[] = [
  {
    id: 1,
    phone: '138****1234',
    personType: '恶意用户',
    reason: '多次恶意刷奖，影响活动公平性',
    addTime: '2024-01-15 14:30:00',
    status: '生效中'
  },
  {
    id: 2,
    phone: '139****5678',
    personType: '刷奖用户',
    reason: '使用多个账号参与抽奖',
    addTime: '2024-01-14 10:20:00',
    status: '生效中'
  },
  {
    id: 3,
    phone: '137****9012',
    personType: '违规用户',
    reason: '违反活动规则，恶意攻击系统',
    addTime: '2024-01-13 16:45:00',
    status: '已解除'
  }
]

// 方法
const fetchBlacklist = () => {
  // 模拟API调用
  blacklist.value = mockBlacklist
  pagination.value.total = mockBlacklist.length
  pagination.value.totalPages = Math.ceil(mockBlacklist.length / pagination.value.pageSize)
  updatePaginationRange()
}

const fetchStats = () => {
  // 模拟统计数据
  stats.value = {
    totalCount: 45,
    monthlyNew: 12,
    maliciousUsers: 23
  }
}

const searchBlacklist = () => {
  console.log('搜索条件:', filters.value)
  fetchBlacklist()
}

const clearFilters = () => {
  filters.value = {
    phone: '',
    personType: ''
  }
  fetchBlacklist()
}

const addToBlacklist = () => {
  if (!newItem.value.phone || !newItem.value.personType) {
    alert('请填写完整信息')
    return
  }
  
  const newBlacklistItem: BlacklistItem = {
    id: Date.now(),
    phone: newItem.value.phone,
    personType: newItem.value.personType,
    reason: newItem.value.reason,
    addTime: new Date().toLocaleString('zh-CN'),
    status: '生效中'
  }
  
  blacklist.value.unshift(newBlacklistItem)
  showAddModal.value = false
  newItem.value = { phone: '', personType: '', reason: '' }
  
  console.log('添加黑名单:', newBlacklistItem)
}

const removeFromBlacklist = (item: BlacklistItem) => {
  if (confirm(`确定要解除 ${item.phone} 的黑名单吗？`)) {
    item.status = '已解除'
    console.log('解除黑名单:', item)
  }
}

const viewDetail = (item: BlacklistItem) => {
  console.log('查看详情:', item)
}

const exportBlacklist = () => {
  console.log('导出黑名单数据')
  // 实现导出逻辑
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    console.log('选择文件:', file.name)
  }
}

const importBlacklist = () => {
  console.log('导入黑名单数据')
  showImportModal.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const updatePaginationRange = () => {
  const { currentPage, pageSize, total } = pagination.value
  pagination.value.start = (currentPage - 1) * pageSize + 1
  pagination.value.end = Math.min(currentPage * pageSize, total)
}

const prevPage = () => {
  if (pagination.value.currentPage > 1) {
    pagination.value.currentPage--
    updatePaginationRange()
    fetchBlacklist()
  }
}

const nextPage = () => {
  if (pagination.value.currentPage < pagination.value.totalPages) {
    pagination.value.currentPage++
    updatePaginationRange()
    fetchBlacklist()
  }
}

// 生命周期
onMounted(() => {
  fetchBlacklist()
  fetchStats()
})
</script> 