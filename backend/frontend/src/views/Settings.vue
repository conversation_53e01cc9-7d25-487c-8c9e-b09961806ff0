<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
      <p class="text-gray-600">配置系统参数和功能设置</p>
    </div>

    <!-- 设置选项卡 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-4 px-1 border-b-2 font-medium text-sm transition-colors"
            :class="activeTab === tab.id 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            <component :is="tab.icon" class="w-4 h-4 inline mr-2" />
            {{ tab.label }}
          </button>
        </nav>
      </div>

      <div class="p-6">
        <!-- 基础设置 -->
        <div v-if="activeTab === 'basic'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">基础设置</h3>
            <div class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">系统名称</label>
                  <input
                    v-model="settings.basic.systemName"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">系统版本</label>
                  <input
                    v-model="settings.basic.version"
                    type="text"
                    readonly
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                  />
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">系统描述</label>
                <textarea
                  v-model="settings.basic.description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                ></textarea>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">联系邮箱</label>
                  <input
                    v-model="settings.basic.contactEmail"
                    type="email"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                  <input
                    v-model="settings.basic.contactPhone"
                    type="tel"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 抽奖设置 -->
        <div v-if="activeTab === 'lottery'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">抽奖设置</h3>
            <div class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">默认每日抽奖次数</label>
                  <input
                    v-model="settings.lottery.defaultDailyLimit"
                    type="number"
                    min="1"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">最大中奖概率 (%)</label>
                  <input
                    v-model="settings.lottery.maxWinRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">抽奖冷却时间 (秒)</label>
                  <input
                    v-model="settings.lottery.cooldownTime"
                    type="number"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    v-model="settings.lottery.requireLogin"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">默认要求用户登录</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.lottery.enableIpLimit"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用IP限制</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.lottery.enableDeviceLimit"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用设备限制</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-if="activeTab === 'notification'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">通知设置</h3>
            <div class="space-y-4">
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    v-model="settings.notification.emailNotification"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用邮件通知</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.notification.smsNotification"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用短信通知</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.notification.pushNotification"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用推送通知</span>
                </label>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">SMTP服务器</label>
                  <input
                    v-model="settings.notification.smtpServer"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">SMTP端口</label>
                  <input
                    v-model="settings.notification.smtpPort"
                    type="number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置 -->
        <div v-if="activeTab === 'security'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">安全设置</h3>
            <div class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">密码最小长度</label>
                  <input
                    v-model="settings.security.minPasswordLength"
                    type="number"
                    min="6"
                    max="20"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">登录失败锁定次数</label>
                  <input
                    v-model="settings.security.maxLoginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">会话超时时间 (分钟)</label>
                  <input
                    v-model="settings.security.sessionTimeout"
                    type="number"
                    min="15"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">IP白名单 (每行一个)</label>
                  <textarea
                    v-model="settings.security.ipWhitelist"
                    rows="3"
                    placeholder="***********&#10;********"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
              </div>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    v-model="settings.security.enableTwoFactor"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用双因素认证</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.security.enableCaptcha"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用验证码</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="settings.security.enableAuditLog"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用审计日志</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
        <button
          @click="resetSettings"
          class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          重置设置
        </button>
        <div class="flex space-x-3">
          <button
            @click="exportSettings"
            class="px-4 py-2 text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors"
          >
            导出配置
          </button>
          <button
            @click="saveSettings"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Settings, Target, Bell, Shield } from 'lucide-vue-next'

const activeTab = ref('basic')

const tabs = [
  { id: 'basic', label: '基础设置', icon: Settings },
  { id: 'lottery', label: '抽奖设置', icon: Target },
  { id: 'notification', label: '通知设置', icon: Bell },
  { id: 'security', label: '安全设置', icon: Shield }
]

const settings = ref({
  basic: {
    systemName: '抽奖管理系统',
    version: '1.0.0',
    description: '专业的抽奖活动管理平台',
    contactEmail: '<EMAIL>',
    contactPhone: '************'
  },
  lottery: {
    defaultDailyLimit: 3,
    maxWinRate: 50,
    cooldownTime: 5,
    requireLogin: true,
    enableIpLimit: true,
    enableDeviceLimit: false
  },
  notification: {
    emailNotification: true,
    smsNotification: false,
    pushNotification: true,
    smtpServer: 'smtp.gmail.com',
    smtpPort: 587
  },
  security: {
    minPasswordLength: 8,
    maxLoginAttempts: 5,
    sessionTimeout: 120,
    ipWhitelist: '',
    enableTwoFactor: false,
    enableCaptcha: true,
    enableAuditLog: true
  }
})

const saveSettings = () => {
  // 保存设置逻辑
  console.log('保存设置:', settings.value)
  alert('设置保存成功！')
}

const resetSettings = () => {
  if (confirm('确定要重置所有设置吗？')) {
    // 重置设置逻辑
    console.log('重置设置')
  }
}

const exportSettings = () => {
  // 导出设置逻辑
  const dataStr = JSON.stringify(settings.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'lottery-settings.json'
  link.click()
  URL.revokeObjectURL(url)
}
</script>