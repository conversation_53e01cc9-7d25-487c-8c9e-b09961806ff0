<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">中奖列表</h1>
      <p class="text-gray-600">查看所有中奖记录和统计信息</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">活动名称</label>
          <input
            v-model="filters.activityName"
            type="text"
            placeholder="搜索活动名称"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">中奖者</label>
          <input
            v-model="filters.winnerName"
            type="text"
            placeholder="搜索中奖者"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">奖品类型</label>
          <select
            v-model="filters.prizeType"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部</option>
            <option value="一等奖">一等奖</option>
            <option value="二等奖">二等奖</option>
            <option value="三等奖">三等奖</option>
            <option value="参与奖">参与奖</option>
          </select>
        </div>
        <div class="flex items-end">
          <button
            @click="searchWinners"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            搜索
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总中奖人数</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalWinners }}</p>
          </div>
          <div class="p-3 bg-blue-100 rounded-full">
            <Trophy class="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">今日中奖</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.todayWinners }}</p>
          </div>
          <div class="p-3 bg-green-100 rounded-full">
            <Calendar class="w-6 h-6 text-green-600" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">一等奖</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.firstPrize }}</p>
          </div>
          <div class="p-3 bg-yellow-100 rounded-full">
            <Star class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总奖品价值</p>
            <p class="text-2xl font-bold text-gray-900">¥{{ stats.totalValue }}</p>
          </div>
          <div class="p-3 bg-purple-100 rounded-full">
            <DollarSign class="w-6 h-6 text-purple-600" />
          </div>
        </div>
      </div>
    </div>

    <!-- 中奖列表表格 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">中奖记录</h3>
          <div class="flex space-x-2">
            <button
              @click="exportWinners"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              导出数据
            </button>
          </div>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                中奖者
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                活动名称
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                奖品
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                中奖时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="winner in winners" :key="winner.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User class="w-4 h-4 text-blue-600" />
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ winner.name }}</div>
                    <div class="text-sm text-gray-500">{{ winner.phone }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ winner.activityName }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Gift class="w-4 h-4 text-yellow-600" />
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ winner.prizeName }}</div>
                    <div class="text-sm text-gray-500">{{ winner.prizeType }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(winner.winTime) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': winner.status === '已发放',
                    'bg-yellow-100 text-yellow-800': winner.status === '待发放',
                    'bg-red-100 text-red-800': winner.status === '已取消'
                  }"
                >
                  {{ winner.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="viewWinnerDetail(winner)"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  查看详情
                </button>
                <button
                  v-if="winner.status === '待发放'"
                  @click="distributePrize(winner)"
                  class="text-green-600 hover:text-green-900"
                >
                  发放奖品
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ pagination.start }} 到 {{ pagination.end }} 条，共 {{ pagination.total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="prevPage"
              :disabled="pagination.currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="px-3 py-1 text-sm text-gray-700">
              {{ pagination.currentPage }} / {{ pagination.totalPages }}
            </span>
            <button
              @click="nextPage"
              :disabled="pagination.currentPage === pagination.totalPages"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Trophy, Calendar, Star, DollarSign, User, Gift } from 'lucide-vue-next'

// 类型定义
interface Winner {
  id: number
  name: string
  phone: string
  activityName: string
  prizeName: string
  prizeType: string
  winTime: string
  status: '已发放' | '待发放' | '已取消'
}

interface Filters {
  activityName: string
  winnerName: string
  prizeType: string
}

interface Stats {
  totalWinners: number
  todayWinners: number
  firstPrize: number
  totalValue: number
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  start: number
  end: number
}

// 响应式数据
const winners = ref<Winner[]>([])
const filters = ref<Filters>({
  activityName: '',
  winnerName: '',
  prizeType: ''
})
const stats = ref<Stats>({
  totalWinners: 0,
  todayWinners: 0,
  firstPrize: 0,
  totalValue: 0
})
const pagination = ref<Pagination>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  start: 0,
  end: 0
})

// 模拟数据
const mockWinners: Winner[] = [
  {
    id: 1,
    name: '张三',
    phone: '138****1234',
    activityName: '新年抽奖活动',
    prizeName: 'iPhone 15',
    prizeType: '一等奖',
    winTime: '2024-01-15 14:30:00',
    status: '已发放'
  },
  {
    id: 2,
    name: '李四',
    phone: '139****5678',
    activityName: '新年抽奖活动',
    prizeName: 'AirPods Pro',
    prizeType: '二等奖',
    winTime: '2024-01-15 15:20:00',
    status: '待发放'
  },
  {
    id: 3,
    name: '王五',
    phone: '137****9012',
    activityName: '春节福利活动',
    prizeName: '小米手环',
    prizeType: '三等奖',
    winTime: '2024-01-16 09:15:00',
    status: '已发放'
  }
]

// 方法
const fetchWinners = () => {
  // 模拟API调用
  winners.value = mockWinners
  pagination.value.total = mockWinners.length
  pagination.value.totalPages = Math.ceil(mockWinners.length / pagination.value.pageSize)
  updatePaginationRange()
}

const fetchStats = () => {
  // 模拟统计数据
  stats.value = {
    totalWinners: 156,
    todayWinners: 12,
    firstPrize: 5,
    totalValue: 125000
  }
}

const searchWinners = () => {
  // 实现搜索逻辑
  console.log('搜索条件:', filters.value)
  fetchWinners()
}

const exportWinners = () => {
  // 实现导出逻辑
  console.log('导出中奖数据')
}

const viewWinnerDetail = (winner: Winner) => {
  console.log('查看中奖详情:', winner)
}

const distributePrize = (winner: Winner) => {
  console.log('发放奖品:', winner)
  // 更新状态
  winner.status = '已发放'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const updatePaginationRange = () => {
  const { currentPage, pageSize, total } = pagination.value
  pagination.value.start = (currentPage - 1) * pageSize + 1
  pagination.value.end = Math.min(currentPage * pageSize, total)
}

const prevPage = () => {
  if (pagination.value.currentPage > 1) {
    pagination.value.currentPage--
    updatePaginationRange()
    fetchWinners()
  }
}

const nextPage = () => {
  if (pagination.value.currentPage < pagination.value.totalPages) {
    pagination.value.currentPage++
    updatePaginationRange()
    fetchWinners()
  }
}

// 生命周期
onMounted(() => {
  fetchWinners()
  fetchStats()
})
</script> 