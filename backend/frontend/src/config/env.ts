// 环境变量配置
export const env = {
  // 应用基础配置
  APP_TITLE: import.meta.env.VITE_APP_TITLE || '奖品管理系统',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // API配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  
  // 环境配置
  NODE_ENV: import.meta.env.VITE_NODE_ENV || 'development',
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  
  // 其他配置
  UPLOAD_URL: import.meta.env.VITE_UPLOAD_URL || 'http://localhost:3000/upload',
  STATIC_URL: import.meta.env.VITE_STATIC_URL || 'http://localhost:3000/static',
}

// 环境判断
export const isDevelopment = env.NODE_ENV === 'development'
export const isProduction = env.NODE_ENV === 'production'
export const isTest = env.NODE_ENV === 'test'

// 导出默认配置
export default env 