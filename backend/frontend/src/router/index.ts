import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import ActivityList from '../views/ActivityList.vue'
import CreateActivity from '../views/CreateActivity.vue'
import PrizeManagement from '../views/PrizeManagement.vue'
import UserManagement from '../views/UserManagement.vue'
import WinnerList from '../views/WinnerList.vue'
import BlacklistManagement from '../views/BlacklistManagement.vue'
import Settings from '../views/Settings.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/activities',
    name: 'ActivityList',
    component: ActivityList
  },
  {
    path: '/activities/create',
    name: 'CreateActivity',
    component: CreateActivity
  },
  {
    path: '/activities/edit/:id',
    name: 'EditActivity',
    component: CreateActivity
  },
  {
    path: '/prizes',
    name: 'PrizeManagement',
    component: PrizeManagement
  },
  {
    path: '/users',
    name: 'UserManagement',
    component: UserManagement
  },
  {
    path: '/winners',
    name: 'WinnerList',
    component: WinnerList
  },
  {
    path: '/blacklist',
    name: 'BlacklistManagement',
    component: BlacklistManagement
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router