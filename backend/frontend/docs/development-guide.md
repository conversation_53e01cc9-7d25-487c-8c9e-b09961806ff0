# 前端开发指南

## 开发环境搭建

### 1. 环境要求

#### Node.js 版本
```bash
# 推荐使用 Node.js 18+ 版本
node --version  # 应该 >= 18.0.0
npm --version   # 应该 >= 8.0.0
```

#### 包管理器
```bash
# 推荐使用 pnpm (更快的包管理)
npm install -g pnpm

# 或者使用 npm
npm install
```

### 2. 项目初始化

#### 克隆项目
```bash
git clone <repository-url>
cd frontend
```

#### 安装依赖
```bash
# 使用 pnpm
pnpm install

# 或使用 npm
npm install
```

#### 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 3. 开发服务器启动

#### 启动开发服务器
```bash
# 使用 pnpm
pnpm dev

# 或使用 npm
npm run dev
```

#### 访问应用
```
http://localhost:5173
```

## 开发流程

### 1. 功能开发流程

#### 创建新功能分支
```bash
# 从主分支创建功能分支
git checkout -b feature/new-feature

# 或使用 Git Flow
git flow feature start new-feature
```

#### 开发步骤
1. **分析需求**: 理解功能需求和设计规范
2. **创建组件**: 在 `src/components/` 或 `src/views/` 中创建组件
3. **编写类型**: 在 `src/types/` 中定义 TypeScript 类型
4. **实现逻辑**: 编写组件逻辑和业务代码
5. **添加样式**: 使用 Tailwind CSS 编写样式
6. **编写测试**: 添加单元测试和集成测试
7. **代码审查**: 提交 PR 进行代码审查

#### 提交代码
```bash
# 添加文件到暂存区
git add .

# 提交代码
git commit -m "feat: 添加新功能描述"

# 推送到远程仓库
git push origin feature/new-feature
```

### 2. 组件开发规范

#### 组件创建模板
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/component'

// 定义 Props
interface Props {
  title: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// 定义 Emits
const emit = defineEmits<{
  update: [value: string]
  delete: [id: number]
}>()

// 响应式数据
const loading = ref(false)
const data = ref<any[]>([])

// 计算属性
const displayTitle = computed(() => `${props.title} (${props.count})`)

// 方法
const handleClick = () => {
  emit('update', 'new value')
}

// 生命周期
onMounted(() => {
  fetchData()
})

// 私有方法
const fetchData = async () => {
  loading.value = true
  try {
    // 获取数据逻辑
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

#### 组件命名规范
```typescript
// 组件文件名使用 PascalCase
UserProfile.vue
ActivityList.vue
PrizeManagement.vue

// 组件名使用 PascalCase
export default {
  name: 'UserProfile'
}
```

### 3. API 开发规范

#### API 接口定义
```typescript
// src/api/user.ts
import { request } from '@/utils/request'
import type { User, CreateUserData, UpdateUserData } from '@/types/user'

export const userApi = {
  // 获取用户列表
  getUsers: (params?: UserQueryParams): Promise<PaginatedResponse<User>> => {
    return request.get('/users', { params })
  },

  // 获取单个用户
  getUser: (id: number): Promise<User> => {
    return request.get(`/users/${id}`)
  },

  // 创建用户
  createUser: (data: CreateUserData): Promise<User> => {
    return request.post('/users', data)
  },

  // 更新用户
  updateUser: (id: number, data: UpdateUserData): Promise<User> => {
    return request.put(`/users/${id}`, data)
  },

  // 删除用户
  deleteUser: (id: number): Promise<void> => {
    return request.delete(`/users/${id}`)
  }
}
```

#### 类型定义
```typescript
// src/types/user.ts
export interface User {
  id: number
  name: string
  email: string
  phone?: string
  avatar?: string
  status: UserStatus
  createdAt: string
  updatedAt: string
}

export interface CreateUserData {
  name: string
  email: string
  phone?: string
  password: string
}

export interface UpdateUserData {
  name?: string
  email?: string
  phone?: string
  status?: UserStatus
}

export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: UserStatus
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}
```

## 调试技巧

### 1. Vue DevTools

#### 安装 Vue DevTools
```bash
# Chrome 扩展
# 访问 Chrome Web Store 搜索 "Vue.js devtools"

# 或使用 npm 安装
npm install -g @vue/devtools
```

#### 使用技巧
- **组件树**: 查看组件层级和状态
- **时间线**: 追踪状态变化
- **性能分析**: 分析组件渲染性能
- **路由**: 查看路由状态和历史

### 2. 浏览器开发者工具

#### Console 调试
```javascript
// 在组件中添加调试代码
console.log('组件数据:', { props, data, computed })

// 使用 debugger 断点
debugger

// 条件日志
if (import.meta.env.DEV) {
  console.log('开发环境日志')
}
```

#### Network 面板
- 查看 API 请求和响应
- 分析请求时间和大小
- 检查请求头和响应头

#### Elements 面板
- 检查 DOM 结构
- 调试 CSS 样式
- 查看事件监听器

### 3. 错误处理

#### 全局错误处理
```typescript
// main.ts
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue 错误:', err)
  console.error('错误信息:', info)
  console.error('组件:', instance?.$options.name)
  
  // 发送错误到监控服务
  errorTracker.captureException(err, {
    extra: { component: instance?.$options.name, info }
  })
}
```

#### 组件错误边界
```vue
<template>
  <ErrorBoundary>
    <template #default>
      <ComponentWithError />
    </template>
    <template #error="{ error }">
      <div class="error-fallback">
        <h3>组件出错了</h3>
        <p>{{ error.message }}</p>
        <button @click="retry">重试</button>
      </div>
    </template>
  </ErrorBoundary>
</template>
```

## 性能优化

### 1. 代码分割

#### 路由懒加载
```typescript
// router/index.ts
const routes = [
  {
    path: '/activities',
    component: () => import('../views/ActivityList.vue')
  },
  {
    path: '/activities/create',
    component: () => import('../views/CreateActivity.vue')
  }
]
```

#### 组件懒加载
```vue
<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

const HeavyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
</script>
```

### 2. 缓存优化

#### 计算属性缓存
```vue
<script setup lang="ts">
// 使用计算属性缓存计算结果
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})
</script>
```

#### 组件缓存
```vue
<template>
  <keep-alive :include="['UserList', 'ActivityList']">
    <router-view />
  </keep-alive>
</template>
```

### 3. 渲染优化

#### 虚拟滚动
```vue
<template>
  <VirtualList
    :items="items"
    :item-height="60"
    :container-height="400"
    v-slot="{ item }"
  >
    <div class="list-item">
      {{ item.name }}
    </div>
  </VirtualList>
</template>
```

#### 防抖和节流
```typescript
// 防抖
const debouncedSearch = debounce((query: string) => {
  searchUsers(query)
}, 300)

// 节流
const throttledScroll = throttle((event: Event) => {
  handleScroll(event)
}, 100)
```

## 测试指南

### 1. 单元测试

#### 组件测试
```typescript
// tests/components/UserProfile.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    const user = {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>'
    }
    
    const wrapper = mount(UserProfile, {
      props: { user }
    })
    
    expect(wrapper.text()).toContain('John Doe')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('emits update event when edit button is clicked', async () => {
    const wrapper = mount(UserProfile, {
      props: { user: mockUser }
    })
    
    await wrapper.find('[data-testid="edit-btn"]').trigger('click')
    
    expect(wrapper.emitted('update')).toBeTruthy()
  })
})
```

#### 工具函数测试
```typescript
// tests/utils/dateUtils.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate, parseDate } from '@/utils/dateUtils'

describe('dateUtils', () => {
  it('formats date correctly', () => {
    const date = new Date('2024-01-01')
    const formatted = formatDate(date)
    expect(formatted).toBe('2024-01-01')
  })
  
  it('parses date string correctly', () => {
    const dateString = '2024-01-01'
    const parsed = parseDate(dateString)
    expect(parsed).toEqual(new Date('2024-01-01'))
  })
})
```

### 2. 集成测试

#### API 测试
```typescript
// tests/api/userApi.test.ts
import { describe, it, expect, vi } from 'vitest'
import { userApi } from '@/api/user'

// Mock axios
vi.mock('@/utils/request', () => ({
  request: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))

describe('userApi', () => {
  it('fetches users successfully', async () => {
    const mockUsers = [
      { id: 1, name: 'John Doe', email: '<EMAIL>' }
    ]
    
    vi.mocked(request.get).mockResolvedValue({
      data: mockUsers,
      total: 1,
      page: 1,
      pageSize: 10
    })
    
    const result = await userApi.getUsers()
    
    expect(result.data).toEqual(mockUsers)
    expect(request.get).toHaveBeenCalledWith('/users', { params: undefined })
  })
})
```

### 3. E2E 测试

#### 页面测试
```typescript
// tests/e2e/activity-management.spec.ts
describe('Activity Management', () => {
  it('should create and list activities', () => {
    cy.visit('/activities')
    
    // 点击创建按钮
    cy.get('[data-testid="create-activity-btn"]').click()
    
    // 填写表单
    cy.get('[data-testid="activity-name"]').type('Test Activity')
    cy.get('[data-testid="activity-description"]').type('Test Description')
    cy.get('[data-testid="start-date"]').type('2024-01-01')
    cy.get('[data-testid="end-date"]').type('2024-01-31')
    
    // 提交表单
    cy.get('[data-testid="submit-btn"]').click()
    
    // 验证跳转
    cy.url().should('include', '/activities')
    
    // 验证列表显示
    cy.contains('Test Activity').should('be.visible')
  })
})
```

## 部署指南

### 1. 构建生产版本

#### 构建命令
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

#### 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          utils: ['axios', 'lodash']
        }
      }
    }
  }
})
```

### 2. 环境配置

#### 生产环境变量
```bash
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_DEBUG=false
VITE_ENABLE_MOCK=false
```

#### 部署脚本
```bash
#!/bin/bash
# deploy.sh

# 构建项目
npm run build

# 压缩构建文件
tar -czf dist.tar.gz dist/

# 上传到服务器
scp dist.tar.gz user@server:/path/to/app/

# 在服务器上解压和部署
ssh user@server "cd /path/to/app && tar -xzf dist.tar.gz"
```

### 3. 服务器配置

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name example.com;
    root /path/to/app/dist;
    index index.html;
    
    # 处理 SPA 路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

### 1. 依赖问题

#### 依赖冲突
```bash
# 清理依赖缓存
rm -rf node_modules package-lock.json
npm install

# 或使用 pnpm
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

#### 版本兼容性
```bash
# 检查依赖版本
npm ls

# 更新依赖
npm update

# 强制重新安装
npm install --force
```

### 2. 构建问题

#### 构建失败
```bash
# 清理构建缓存
rm -rf dist
npm run build

# 检查 TypeScript 错误
npx vue-tsc --noEmit
```

#### 性能问题
```bash
# 分析构建包大小
npm run build -- --analyze

# 检查重复依赖
npx depcheck
```

### 3. 运行时问题

#### 路由问题
```typescript
// 检查路由配置
console.log('当前路由:', router.currentRoute.value)
console.log('路由历史:', router.getRoutes())
```

#### 状态管理问题
```typescript
// 调试 Pinia store
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()

// 在开发环境中启用调试
if (import.meta.env.DEV) {
  console.log('用户状态:', userStore.$state)
}
```

## 最佳实践

### 1. 代码组织
- 按功能模块组织代码
- 保持组件单一职责
- 使用 TypeScript 严格模式
- 编写清晰的注释和文档

### 2. 性能优化
- 使用懒加载减少初始包大小
- 合理使用缓存机制
- 优化图片和静态资源
- 监控性能指标

### 3. 安全考虑
- 验证用户输入
- 防止 XSS 攻击
- 使用 HTTPS
- 保护敏感信息

### 4. 用户体验
- 提供加载状态反馈
- 处理错误情况
- 支持键盘导航
- 确保响应式设计

---

*本开发指南会随着项目发展持续更新* 