# 前端架构设计文档

## 架构概述

本前端项目采用现代化的分层架构设计，基于 Vue 3 生态系统构建，具备良好的可扩展性、可维护性和性能表现。

## 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户界面层                            │
├─────────────────────────────────────────────────────────────┤
│  Layout.vue  │  Dashboard.vue  │  ActivityList.vue  │  ...  │
├─────────────────────────────────────────────────────────────┤
│                        组件层                               │
├─────────────────────────────────────────────────────────────┤
│  HelloWorld.vue  │  自定义组件  │  第三方组件库            │
├─────────────────────────────────────────────────────────────┤
│                        业务逻辑层                           │
├─────────────────────────────────────────────────────────────┤
│  Router  │  Stores  │  Utils  │  API  │  Config           │
├─────────────────────────────────────────────────────────────┤
│                        数据层                               │
├─────────────────────────────────────────────────────────────┤
│  HTTP API  │  LocalStorage  │  SessionStorage  │  IndexedDB │
└─────────────────────────────────────────────────────────────┘
```

## 分层架构详解

### 1. 用户界面层 (UI Layer)

#### 职责
- 用户交互界面展示
- 用户输入处理
- 视觉反馈和动画

#### 组件分类
```typescript
// 页面级组件 (Views)
- Dashboard.vue      // 仪表盘页面
- ActivityList.vue   // 活动列表页面
- CreateActivity.vue // 活动创建页面
- PrizeManagement.vue // 奖品管理页面
- UserManagement.vue  // 用户管理页面
- DrawRecords.vue     // 抽奖记录页面
- Settings.vue        // 系统设置页面

// 布局组件 (Layout)
- Layout.vue         // 主布局组件

// 通用组件 (Components)
- HelloWorld.vue     // 示例组件
```

#### 设计原则
- **单一职责**: 每个组件只负责一个特定功能
- **可复用性**: 组件设计通用化，支持多种使用场景
- **响应式**: 支持多种屏幕尺寸和设备类型
- **无障碍**: 遵循 WCAG 2.1 无障碍设计标准

### 2. 组件层 (Component Layer)

#### 组件通信模式
```vue
<!-- 父子组件通信 -->
<template>
  <UserList 
    :users="users" 
    @user-selected="handleUserSelect"
    @user-deleted="handleUserDelete"
  />
</template>

<!-- 兄弟组件通信 -->
<script setup lang="ts">
// 使用 provide/inject 进行跨层级通信
const userStore = useUserStore()
provide('userStore', userStore)
</script>
```

#### 组件生命周期管理
```typescript
// 组件挂载时初始化
onMounted(() => {
  fetchInitialData()
  setupEventListeners()
})

// 组件卸载时清理
onUnmounted(() => {
  cleanupEventListeners()
  cancelPendingRequests()
})
```

### 3. 业务逻辑层 (Business Logic Layer)

#### 路由管理 (Router)
```typescript
// 路由配置结构
interface RouteConfig {
  path: string
  name: string
  component: Component
  meta: {
    title: string
    requiresAuth: boolean
    permissions?: string[]
  }
  children?: RouteConfig[]
}

// 路由守卫
router.beforeEach((to, from, next) => {
  // 权限验证
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
    return
  }
  
  // 页面标题设置
  document.title = `${to.meta.title} - 抽奖管理系统`
  
  next()
})
```

#### 状态管理 (Stores)
```typescript
// Pinia Store 设计模式
export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  
  // 操作
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authApi.login(credentials)
      user.value = response.data
      return response
    } catch (error) {
      throw error
    }
  }
  
  const logout = () => {
    user.value = null
    localStorage.removeItem('token')
  }
  
  return {
    user,
    isAuthenticated,
    login,
    logout
  }
})
```

#### 工具函数 (Utils)
```typescript
// 请求工具
class ApiClient {
  private instance: AxiosInstance
  
  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      timeout: 10000
    })
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => response.data,
      (error) => this.handleError(error)
    )
  }
  
  private handleError(error: AxiosError) {
    if (error.response?.status === 401) {
      // 未授权，跳转登录
      router.push('/login')
    }
    return Promise.reject(error)
  }
  
  get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }
  
  post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }
}
```

### 4. 数据层 (Data Layer)

#### API 接口设计
```typescript
// API 接口类型定义
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应类型
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 活动相关 API
export const activityApi = {
  // 获取活动列表
  getActivities: (params: ActivityQueryParams): Promise<PaginatedResponse<Activity>> => {
    return apiClient.get('/activities', { params })
  },
  
  // 创建活动
  createActivity: (data: CreateActivityData): Promise<Activity> => {
    return apiClient.post('/activities', data)
  },
  
  // 更新活动
  updateActivity: (id: number, data: UpdateActivityData): Promise<Activity> => {
    return apiClient.put(`/activities/${id}`, data)
  },
  
  // 删除活动
  deleteActivity: (id: number): Promise<void> => {
    return apiClient.delete(`/activities/${id}`)
  }
}
```

#### 本地存储策略
```typescript
// 本地存储管理
class StorageManager {
  private static instance: StorageManager
  
  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager()
    }
    return StorageManager.instance
  }
  
  // 设置数据
  set<T>(key: string, value: T, storage: 'local' | 'session' = 'local'): void {
    const targetStorage = storage === 'local' ? localStorage : sessionStorage
    targetStorage.setItem(key, JSON.stringify(value))
  }
  
  // 获取数据
  get<T>(key: string, storage: 'local' | 'session' = 'local'): T | null {
    const targetStorage = storage === 'local' ? localStorage : sessionStorage
    const value = targetStorage.getItem(key)
    return value ? JSON.parse(value) : null
  }
  
  // 删除数据
  remove(key: string, storage: 'local' | 'session' = 'local'): void {
    const targetStorage = storage === 'local' ? localStorage : sessionStorage
    targetStorage.removeItem(key)
  }
  
  // 清空所有数据
  clear(storage: 'local' | 'session' = 'local'): void {
    const targetStorage = storage === 'local' ? localStorage : sessionStorage
    targetStorage.clear()
  }
}
```

## 设计模式应用

### 1. 单例模式 (Singleton)
```typescript
// API 客户端单例
export const apiClient = ApiClient.getInstance()

// 存储管理器单例
export const storageManager = StorageManager.getInstance()
```

### 2. 工厂模式 (Factory)
```typescript
// 组件工厂
class ComponentFactory {
  static createButton(type: 'primary' | 'secondary' | 'danger', props: any) {
    switch (type) {
      case 'primary':
        return h('button', { class: 'btn btn-primary', ...props })
      case 'secondary':
        return h('button', { class: 'btn btn-secondary', ...props })
      case 'danger':
        return h('button', { class: 'btn btn-danger', ...props })
      default:
        return h('button', { class: 'btn', ...props })
    }
  }
}
```

### 3. 观察者模式 (Observer)
```typescript
// 事件总线
class EventBus {
  private events: Map<string, Function[]> = new Map()
  
  on(event: string, callback: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }
  
  emit(event: string, data?: any): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }
  
  off(event: string, callback: Function): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }
}

export const eventBus = new EventBus()
```

### 4. 策略模式 (Strategy)
```typescript
// 表单验证策略
interface ValidationStrategy {
  validate(value: any): boolean
  getMessage(): string
}

class EmailValidationStrategy implements ValidationStrategy {
  validate(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  }
  
  getMessage(): string {
    return '请输入有效的邮箱地址'
  }
}

class PhoneValidationStrategy implements ValidationStrategy {
  validate(value: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(value)
  }
  
  getMessage(): string {
    return '请输入有效的手机号码'
  }
}

// 验证器
class Validator {
  private strategies: Map<string, ValidationStrategy> = new Map()
  
  addStrategy(name: string, strategy: ValidationStrategy): void {
    this.strategies.set(name, strategy)
  }
  
  validate(name: string, value: any): { isValid: boolean; message: string } {
    const strategy = this.strategies.get(name)
    if (!strategy) {
      return { isValid: true, message: '' }
    }
    
    const isValid = strategy.validate(value)
    return {
      isValid,
      message: isValid ? '' : strategy.getMessage()
    }
  }
}
```

## 性能优化策略

### 1. 代码分割 (Code Splitting)
```typescript
// 路由级别的代码分割
const routes = [
  {
    path: '/activities',
    component: () => import('../views/ActivityList.vue')
  },
  {
    path: '/activities/create',
    component: () => import('../views/CreateActivity.vue')
  }
]

// 组件级别的代码分割
const LazyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
```

### 2. 虚拟滚动 (Virtual Scrolling)
```vue
<template>
  <VirtualList
    :items="items"
    :item-height="60"
    :container-height="400"
    v-slot="{ item }"
  >
    <div class="list-item">
      {{ item.name }}
    </div>
  </VirtualList>
</template>
```

### 3. 缓存策略 (Caching Strategy)
```typescript
// 内存缓存
class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private maxAge = 5 * 60 * 1000 // 5分钟
  
  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > this.maxAge) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  clear(): void {
    this.cache.clear()
  }
}
```

### 4. 图片优化 (Image Optimization)
```typescript
// 图片懒加载
const useLazyImage = () => {
  const imageRef = ref<HTMLImageElement>()
  const isLoaded = ref(false)
  
  const loadImage = () => {
    if (imageRef.value) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = img.dataset.src!
            observer.unobserve(img)
            isLoaded.value = true
          }
        })
      })
      
      observer.observe(imageRef.value)
    }
  }
  
  onMounted(loadImage)
  
  return {
    imageRef,
    isLoaded
  }
}
```

## 安全设计

### 1. XSS 防护
```typescript
// 输入净化
const sanitizeInput = (input: string): string => {
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

// 输出转义
const escapeHtml = (str: string): string => {
  const div = document.createElement('div')
  div.textContent = str
  return div.innerHTML
}
```

### 2. CSRF 防护
```typescript
// CSRF Token 管理
class CSRFManager {
  private token: string | null = null
  
  getToken(): string | null {
    if (!this.token) {
      this.token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || null
    }
    return this.token
  }
  
  setToken(token: string): void {
    this.token = token
  }
  
  // 在请求头中添加 CSRF Token
  addCSRFToken(headers: Record<string, string>): Record<string, string> {
    const token = this.getToken()
    if (token) {
      headers['X-CSRF-Token'] = token
    }
    return headers
  }
}
```

### 3. 权限控制
```typescript
// 权限检查
class PermissionManager {
  private permissions: Set<string> = new Set()
  
  setPermissions(permissions: string[]): void {
    this.permissions.clear()
    permissions.forEach(permission => this.permissions.add(permission))
  }
  
  hasPermission(permission: string): boolean {
    return this.permissions.has(permission)
  }
  
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.permissions.has(permission))
  }
  
  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.permissions.has(permission))
  }
}

// 权限指令
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const permissionManager = usePermissionManager()
    
    if (!permissionManager.hasPermission(value)) {
      el.style.display = 'none'
    }
  }
}
```

## 测试策略

### 1. 单元测试
```typescript
// 组件测试
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserProfile from '../UserProfile.vue'

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    const user = {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>'
    }
    
    const wrapper = mount(UserProfile, {
      props: { user }
    })
    
    expect(wrapper.text()).toContain('John Doe')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
})
```

### 2. 集成测试
```typescript
// API 集成测试
describe('Activity API', () => {
  it('should create activity successfully', async () => {
    const activityData = {
      name: 'Test Activity',
      description: 'Test Description',
      startDate: '2024-01-01',
      endDate: '2024-01-31'
    }
    
    const response = await activityApi.createActivity(activityData)
    
    expect(response).toHaveProperty('id')
    expect(response.name).toBe(activityData.name)
  })
})
```

### 3. E2E 测试
```typescript
// 端到端测试
describe('Activity Management', () => {
  it('should create and list activities', () => {
    cy.visit('/activities')
    cy.get('[data-testid="create-activity-btn"]').click()
    cy.get('[data-testid="activity-name"]').type('Test Activity')
    cy.get('[data-testid="activity-description"]').type('Test Description')
    cy.get('[data-testid="submit-btn"]').click()
    cy.url().should('include', '/activities')
    cy.contains('Test Activity').should('be.visible')
  })
})
```

## 部署架构

### 1. 构建优化
```typescript
// Vite 构建配置
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          utils: ['axios', 'lodash'],
          ui: ['element-plus']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

### 2. 环境配置
```bash
# 开发环境
VITE_API_BASE_URL=http://localhost:3000/api
VITE_DEBUG=true
VITE_ENABLE_MOCK=true

# 测试环境
VITE_API_BASE_URL=https://test-api.example.com/api
VITE_DEBUG=true
VITE_ENABLE_MOCK=false

# 生产环境
VITE_API_BASE_URL=https://api.example.com/api
VITE_DEBUG=false
VITE_ENABLE_MOCK=false
```

### 3. CDN 配置
```html
<!-- 使用 CDN 加速静态资源 -->
<script src="https://cdn.jsdelivr.net/npm/vue@3.4.38/dist/vue.global.prod.js"></script>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.4.0/dist/tailwind.min.css" rel="stylesheet">
```

## 监控和日志

### 1. 错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  // 发送错误到监控服务
  errorTracker.captureException(event.error)
})

// Vue 错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue error:', err, info)
  errorTracker.captureException(err, {
    extra: { component: instance?.$options.name, info }
  })
}
```

### 2. 性能监控
```typescript
// 性能指标收集
const performanceMonitor = {
  measurePageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const loadTime = navigation.loadEventEnd - navigation.loadEventStart
      
      console.log('Page load time:', loadTime)
      // 发送到监控服务
    })
  },
  
  measureApiCall(url: string, startTime: number) {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.log(`API call to ${url} took ${duration}ms`)
    // 发送到监控服务
  }
}
```

---

*本架构文档会随着项目发展持续更新和完善* 