# 奖品系统后端 (PrizeSystemBe)

## 项目简介

奖品系统后端是一个基于Spring Boot 3.2.3开发的抽奖活动管理系统，提供完整的抽奖流程管理、用户管理、活动管理等功能。具备完善的权限控制和数据安全保护机制。

## 技术栈

### 核心框架
- **Spring Boot 3.2.3** - 主框架
- **Java 17** - 开发语言
- **Maven** - 项目管理工具

### 数据库与缓存
- **MySQL** - 主数据库
- **Redis** - 缓存数据库（可选）
- **MyBatis-Plus 3.5.5** - ORM框架
- **Druid 1.2.21** - 数据库连接池

### 工具库
- **Lombok** - 代码简化工具
- **Hutool 5.8.27** - 工具类库
- **FastJSON 1.2.70** - JSON处理
- **Apache POI 5.2.3** - Excel处理

### 安全与认证
- **JWT 0.9.1** - 令牌认证
- **阿里云短信服务** - 短信验证码

### 文件存储
- **阿里云OSS** - 对象存储服务

### API文档
- **Knife4j 4.4.0** - API文档工具
- **Swagger 3** - API规范

## 功能特性

### 🎯 核心功能
- **抽奖流程管理** - 完整的抽奖流程控制
- **活动管理** - 活动创建、配置、监控
- **用户管理** - 用户信息、黑名单管理
- **奖品管理** - 奖品配置、概率设置
- **文件上传** - 图片上传到阿里云OSS

### 🔐 安全特性
- **API签名验证** - 接口安全防护
- **JWT令牌认证** - 用户身份验证
- **短信验证码** - 手机号验证
- **用户黑名单** - 风险用户管理

### 📊 数据管理
- **抽奖记录** - 完整的抽奖历史
- **用户统计** - 用户抽奖次数统计
- **活动监控** - 活动数据实时监控

## 项目结构

```
prize_system/
├── src/main/java/yogu/pro/
│   ├── annotation/          # 自定义注解
│   ├── config/             # 配置类
│   ├── constant/           # 常量定义
│   ├── controller/         # 控制器层
│   ├── handler/            # 全局异常处理
│   ├── interceptors/       # 拦截器
│   ├── mapper/            # 数据访问层
│   ├── pojo/              # 实体类
│   │   ├── dto/           # 数据传输对象
│   │   └── po/            # 持久化对象
│   ├── result/            # 统一返回结果
│   ├── service/           # 业务逻辑层
│   │   └── impl/         # 业务实现类
│   ├── util/              # 工具类
│   └── PrizeSystemBeApplication.java  # 启动类
├── src/main/resources/
│   ├── application-*.yaml  # 配置文件
│   ├── mapper/            # MyBatis映射文件
│   └── sql/               # SQL脚本
└── pom.xml                # Maven配置
```

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+ (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd prize_system
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE prize_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **修改配置文件**
编辑 `src/main/resources/application-dev.yaml`：
```yaml
spring:
  datasource:
    druid:
      url: ****************************************?useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8
      username: your_username
      password: your_password
```

4. **配置阿里云服务**
```yaml
# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-beijing.aliyuncs.com
    access-key-id: your_access_key_id
    access-key-secret: your_access_key_secret
    bucket-name: your_bucket_name
    domain: https://your-bucket.oss-cn-beijing.aliyuncs.com
```

5. **启动应用**
```bash
mvn clean install
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 访问地址
- **应用地址**: http://localhost:8080
- **API文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui.html

## API接口

### 抽奖相关接口

#### 获取抽奖机会
```http
POST /lotteryDraw/getDrawOpportunity
Content-Type: application/json

{
  "phoneNumber": "13800138000",
  "code": "123456",
  "activityId": 1
}
```

#### 执行抽奖
```http
POST /lotteryDraw/performDraw?phoneNumber=13800138000&activityId=1
```

### 文件上传接口

#### 上传图片到OSS
```http
POST /fileUpload/uploadOSS
Content-Type: multipart/form-data

photo: [文件]
model: "activity"
```

#### 删除OSS文件
```http
POST /fileUpload/deleteOSS?fileUrl=https://example.com/image.jpg
```

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************
      username: root
      password: password
      initial-size: 3
      min-idle: 3
      max-active: 15
```

### Redis配置（可选）
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
```

### API认证配置
```yaml
api:
  auth:
    enabled: true
    app-id: your_app_id
    app-key: your_app_key
```

## 部署指南

### 开发环境
```bash
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 生产环境
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 运行
java -jar target/PrizeSystemBe-1.0.0.jar --spring.profiles.active=prod
```

### Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/PrizeSystemBe-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一使用Result包装返回结果
- 使用Swagger注解文档化API

### 异常处理
- 全局异常处理器：`GlobalExceptionHandler`
- 统一返回格式：`Result<T>`
- 自定义业务异常

### 日志配置
```yaml
logging:
  level:
    yogu.pro: debug
  pattern:
    dateformat: HH:mm:ss
```

## 监控与维护

### 健康检查
- 数据库连接状态监控
- Redis连接状态监控（如果启用）
- 应用运行状态监控

### 日志管理
- 应用日志：`logs/application.log`
- 错误日志：`logs/error.log`
- 访问日志：`logs/access.log`

## 常见问题

### Q: 如何修改数据库连接配置？
A: 编辑 `application-dev.yaml` 中的 `spring.datasource.druid` 配置项。

### Q: 如何启用Redis缓存？
A: 取消注释 `application-dev.yaml` 中的Redis配置，并确保Redis服务正常运行。

### Q: 如何自定义API认证？
A: 修改 `ApiSignatureInterceptor` 中的认证逻辑。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者：mcayan
- 邮箱：[<EMAIL>]
- 项目地址：[项目GitHub地址]

---

**注意**: 请确保在生产环境中修改默认的数据库密码、API密钥等敏感信息。 