package yogu.pro.service;

import yogu.pro.pojo.dto.DrawOpportunityResponseDTO;
import yogu.pro.result.Result;

import yogu.pro.pojo.dto.LotteryResultDTO;

/**
 * 抽奖服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ILotteryDrawService {
    
    /**
     * 校验资质并增加抽奖次数
     * 
     * @param phoneNumber 手机号
     * @param code 二维码验证码
     * @param activityId 活动ID
     * @return 结果
     */
    Result<DrawOpportunityResponseDTO> getDrawOpportunity(String phoneNumber, String code, Long activityId);
    
    /**
     * 执行抽奖（消耗抽奖次数）
     * 
     * @param phoneNumber 手机号
     * @param activityId 活动ID
     * @return 抽奖结果
     */
    Result<LotteryResultDTO> performDraw(String phoneNumber, Long activityId);
} 