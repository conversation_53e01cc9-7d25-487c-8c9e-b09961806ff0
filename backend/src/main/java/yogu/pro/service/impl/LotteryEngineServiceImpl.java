package yogu.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yogu.pro.pojo.po.ActivityPrizeProb;
import yogu.pro.service.IActivityPrizeProbService;
import yogu.pro.service.ILotteryEngineService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Random;

/**
 * 抽奖引擎服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LotteryEngineServiceImpl implements ILotteryEngineService {
    
    private final IActivityPrizeProbService activityPrizeProbService;
    private final Random random = new Random();
    
    @Override
    public ActivityPrizeProb performLottery(Long activityId) {
        try {
            // 1. 查询活动奖品配置
            LambdaQueryWrapper<ActivityPrizeProb> query = new LambdaQueryWrapper<>();
            query.eq(ActivityPrizeProb::getActivityId, activityId)
                    // 只查询有库存的奖品
                 .gt(ActivityPrizeProb::getPrizeStock, 0);
            
            List<ActivityPrizeProb> prizes = activityPrizeProbService.list(query);
            
            if (prizes.isEmpty()) {
                log.warn("活动 {} 没有可用的奖品", activityId);
                return null;
            }
            
            // 2. 使用正态分布算法选择奖品
            ActivityPrizeProb selectedPrize = selectPrizeByNormalDistribution(prizes);
            
            if (selectedPrize != null) {
                log.info("活动 {} 抽奖结果: 奖品ID={}, 奖品名称={}, 概率={}%", 
                        activityId, selectedPrize.getId(), selectedPrize.getPrizeName(), 
                        selectedPrize.getWinProb());
            }
            
            return selectedPrize;
            
        } catch (Exception e) {
            log.error("抽奖引擎执行异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public double generateNormalDistribution(double mean, double standardDeviation) {
        // Box-Muller 变换生成正态分布随机数
        double u1 = random.nextDouble();
        double u2 = random.nextDouble();
        
        // 避免 u1 为 0 的情况
        if (u1 == 0) {
            u1 = 0.0001;
        }
        
        double z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
        
        return mean + standardDeviation * z0;
    }
    
    @Override
    public ActivityPrizeProb selectPrizeByWeight(List<ActivityPrizeProb> prizes) {
        if (prizes == null || prizes.isEmpty()) {
            return null;
        }
        
        // 计算总权重
        double totalWeight = prizes.stream()
                .mapToDouble(prize -> prize.getWinProb().doubleValue())
                .sum();
        
        if (totalWeight <= 0) {
            return null;
        }
        
        // 生成随机数
        double randomValue = random.nextDouble() * totalWeight;
        
        // 根据权重选择奖品
        double currentWeight = 0;
        for (ActivityPrizeProb prize : prizes) {
            currentWeight += prize.getWinProb().doubleValue();
            if (randomValue <= currentWeight) {
                return prize;
            }
        }
        
        // 兜底返回最后一个奖品
        return prizes.get(prizes.size() - 1);
    }
    
    /**
     * 使用正态分布算法选择奖品
     * 
     * @param prizes 奖品列表
     * @return 选中的奖品
     */
    private ActivityPrizeProb selectPrizeByNormalDistribution(List<ActivityPrizeProb> prizes) {
        if (prizes == null || prizes.isEmpty()) {
            return null;
        }
        
        // 计算奖品概率的均值和标准差
        double mean = prizes.stream()
                .mapToDouble(prize -> prize.getWinProb().doubleValue())
                .average()
                .orElse(0.0);
        
        double variance = prizes.stream()
                .mapToDouble(prize -> Math.pow(prize.getWinProb().doubleValue() - mean, 2))
                .average()
                .orElse(0.0);
        
        double standardDeviation = Math.sqrt(variance);
        
        // 生成正态分布随机数
        double randomValue = generateNormalDistribution(mean, standardDeviation);
        
        // 根据正态分布结果选择奖品
        ActivityPrizeProb selectedPrize = null;
        double minDifference = Double.MAX_VALUE;
        
        for (ActivityPrizeProb prize : prizes) {
            double difference = Math.abs(prize.getWinProb().doubleValue() - randomValue);
            if (difference < minDifference) {
                minDifference = difference;
                selectedPrize = prize;
            }
        }
        
        return selectedPrize;
    }
} 