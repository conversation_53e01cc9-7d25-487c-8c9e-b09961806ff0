package yogu.pro.service.impl;

import yogu.pro.pojo.po.ActivityDrawSettings;
import yogu.pro.mapper.ActivityDrawSettingsMapper;
import yogu.pro.service.IActivityDrawSettingsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖活动规则设置表（含积分、次数、城市等限制） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ActivityDrawSettingsServiceImpl extends ServiceImpl<ActivityDrawSettingsMapper, ActivityDrawSettings> implements IActivityDrawSettingsService {

}
