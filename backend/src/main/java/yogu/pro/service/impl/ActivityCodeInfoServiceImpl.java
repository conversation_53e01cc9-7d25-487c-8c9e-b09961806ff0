package yogu.pro.service.impl;

import yogu.pro.pojo.po.ActivityCodeInfo;
import yogu.pro.mapper.ActivityCodeInfoMapper;
import yogu.pro.service.IActivityCodeInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖活动二维码信息表（无自增ID，code_value作为唯一标识） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ActivityCodeInfoServiceImpl extends ServiceImpl<ActivityCodeInfoMapper, ActivityCodeInfo> implements IActivityCodeInfoService {

}
