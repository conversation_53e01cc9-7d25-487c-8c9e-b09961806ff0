package yogu.pro.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import yogu.pro.properties.OssProperties;
import yogu.pro.service.FileUploadService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {
    
    @Autowired
    private OssProperties ossProperties;
    
    @Override
    public Map<String, Object> uploadImage(MultipartFile file, String module) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 验证文件
            if (!isValidImage(file)) {
                result.put("success", false);
                result.put("message", "无效的图片文件");
                return result;
            }
            
            // 2. 检查文件大小
            long fileSizeInMB = file.getSize() / (1024 * 1024);
            if (fileSizeInMB > ossProperties.getMaxFileSize()) {
                result.put("success", false);
                result.put("message", "文件大小超过限制，最大允许" + ossProperties.getMaxFileSize() + "MB");
                return result;
            }
            
            // 3. 生成唯一文件名
            String originalFileName = file.getOriginalFilename();
            String fileName = generateUniqueFileName(originalFileName, module);
            
            // 4. 构建完整的文件路径
            String filePath = ossProperties.getPrefix() + module + "/" + fileName;
            
            // 5. 上传到OSS
            String fileUrl = uploadToOss(file, filePath);
            
            if (fileUrl != null) {
                result.put("success", true);
                result.put("message", "上传成功");
                result.put("fileName", fileName);
                result.put("originalName", originalFileName);
                result.put("fileUrl", fileUrl);
                result.put("fileSize", file.getSize());
                result.put("contentType", file.getContentType());
                
                log.info("文件上传成功: {}", fileUrl);
            } else {
                result.put("success", false);
                result.put("message", "上传失败");
            }
            
        } catch (Exception e) {
            log.error("文件上传异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "上传异常: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public boolean isValidImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        // 检查文件扩展名
        String originalFileName = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFileName)) {
            return false;
        }
        
        String extension = getFileExtension(originalFileName);
        if (!StringUtils.hasText(extension)) {
            return false;
        }
        
        // 检查是否在允许的扩展名列表中
        return ossProperties.getAllowedExtensions().contains(extension.toLowerCase());
    }
    
    @Override
    public String generateUniqueFileName(String originalFileName, String module) {
        String extension = getFileExtension(originalFileName);
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uniqueId = UUID.randomUUID().toString().replace("-", "");
        return datePath + "/" + module + "_" + uniqueId + "." + extension;
    }
    
    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            if (!StringUtils.hasText(fileUrl)) {
                return false;
            }
            
            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            if (!StringUtils.hasText(filePath)) {
                log.warn("无法从URL中提取文件路径: {}", fileUrl);
                return false;
            }
            
            // 删除OSS文件
            OSS ossClient = createOssClient();
            try {
                ossClient.deleteObject(ossProperties.getBucketName(), filePath);
                log.info("文件删除成功: {}", filePath);
                return true;
            } finally {
                ossClient.shutdown();
            }
            
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 上传文件到OSS
     */
    private String uploadToOss(MultipartFile file, String filePath) {
        try {
            // 检查OSS配置是否有效
            if (!isOssConfigValid()) {
                log.warn("OSS配置无效，使用模拟上传模式");
                // 返回模拟的URL
                return "https://mock-oss-domain.com/" + filePath + "?mock=true&size=" + file.getSize();
            }
            
            OSS ossClient = createOssClient();
            try {
                // 创建上传请求
                PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossProperties.getBucketName(), 
                    filePath, 
                    file.getInputStream()
                );
                
                // 执行上传
                PutObjectResult result = ossClient.putObject(putObjectRequest);
                
                if (result != null) {
                    // 构建文件访问URL
                    return ossProperties.getDomain() + "/" + filePath;
                }
                
                return null;
                
            } finally {
                ossClient.shutdown();
            }
            
        } catch (Exception e) {
            log.error("OSS上传失败: {}, 使用模拟模式", e.getMessage());
            // 返回模拟的URL作为降级方案
            return "https://mock-oss-domain.com/" + filePath + "?mock=true&error=" + e.getMessage();
        }
    }
    
    /**
     * 检查OSS配置是否有效
     */
    private boolean isOssConfigValid() {
        return ossProperties != null 
            && StringUtils.hasText(ossProperties.getAccessKeyId())
            && StringUtils.hasText(ossProperties.getAccessKeySecret())
            && StringUtils.hasText(ossProperties.getBucketName())
            && StringUtils.hasText(ossProperties.getEndpoint())
            && !ossProperties.getAccessKeyId().equals("YOUR_ACCESS_KEY_ID")
            && !ossProperties.getAccessKeySecret().equals("YOUR_ACCESS_KEY_SECRET")
            && !ossProperties.getBucketName().equals("your-bucket-name");
    }
    
    /**
     * 创建OSS客户端
     */
    private OSS createOssClient() {
        return new OSSClientBuilder().build(
            ossProperties.getEndpoint(),
            ossProperties.getAccessKeyId(),
            ossProperties.getAccessKeySecret()
        );
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String fileUrl) {
        try {
            String domain = ossProperties.getDomain();
            if (fileUrl.startsWith(domain + "/")) {
                return fileUrl.substring(domain.length() + 1);
            }
            
            // 如果不是当前域名的URL，尝试其他方式解析
            if (fileUrl.contains(ossProperties.getBucketName())) {
                int index = fileUrl.indexOf(ossProperties.getPrefix());
                if (index != -1) {
                    return fileUrl.substring(index);
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("提取文件路径失败: {}", e.getMessage(), e);
            return null;
        }
    }
} 