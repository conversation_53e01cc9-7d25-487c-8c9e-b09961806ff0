package yogu.pro.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 抽奖活动提示语+UI配置合并表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_prompt_ui")
public class ActivityPromptUi implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联活动主表ID
     */
    @TableField(value = "activity_id")
    private Long activityId;

    /**
     * 未中奖提示语
     */
    @TableField(value = "prompt_unwon")
    private String promptUnwon;

    /**
     * 已中奖提示语
     */
    @TableField(value = "prompt_won")
    private String promptWon;

    /**
     * 无权限参与提示语
     */
    @TableField(value = "prompt_no_permission")
    private String promptNoPermission;

    /**
     * 抽奖已达次数上限提示语
     */
    @TableField(value = "prompt_reach_limit")
    private String promptReachLimit;

    /**
     * 活动未开始提示语
     */
    @TableField(value = "prompt_not_started")
    private String promptNotStarted;

    /**
     * 活动已结束提示语
     */
    @TableField(value = "prompt_ended")
    private String promptEnded;

    /**
     * 抽奖背景图OSS地址
     */
    @TableField(value = "ui_bg_image_url")
    private String uiBgImageUrl;

    /**
     * 领红包弹窗图OSS地址
     */
    @TableField(value = "ui_popup_image_url")
    private String uiPopupImageUrl;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;


}
