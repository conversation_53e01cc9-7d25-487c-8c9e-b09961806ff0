package yogu.pro.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import yogu.pro.annotation.NoLogin;
import yogu.pro.pojo.dto.DrawOpportunityResponseDTO;
import yogu.pro.pojo.dto.GetDrawOpportunityDTO;
import yogu.pro.result.Result;
import yogu.pro.service.ILotteryDrawService;

/**
 * <AUTHOR>
 * @date ：2025/7/30 15:54
 */
@RestController
@RequestMapping("/lotteryDraw")
@Tag(name = "抽奖流程相关接口")
@RequiredArgsConstructor
@Slf4j
public class LotteryDrawController {

    private final ILotteryDrawService lotteryDrawService;


    //TODO 地理位置限制接口(入参活动id 以及 当前用户ip属地)


    @PostMapping("/getDrawOpportunity")
    @Operation(summary = "校验资质并增加抽奖次数", description = "通过二维码验证码校验用户资质，成功后增加抽奖次数")
    @NoLogin
    public Result<DrawOpportunityResponseDTO> getDrawOpportunity(@Valid @RequestBody GetDrawOpportunityDTO request) {
        return lotteryDrawService.getDrawOpportunity(request.getPhoneNumber(), request.getCode(), request.getActivityId());
    }
    
    @PostMapping("/performDraw")
    @Operation(summary = "执行抽奖", description = "消耗抽奖次数进行实际抽奖")
    @NoLogin
    public Result performDraw(@RequestParam("phoneNumber") String phoneNumber,
                            @RequestParam("activityId") Long activityId) {
        return lotteryDrawService.performDraw(phoneNumber, activityId);
    }
}
