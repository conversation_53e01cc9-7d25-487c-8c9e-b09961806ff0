package yogu.pro.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import yogu.pro.annotation.NoLogin;
import yogu.pro.result.Result;
import yogu.pro.service.FileUploadService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2025/7/30 09:49
 */
@RestController
@RequestMapping("/fileUpload")
@Tag(name = "文件上传相关接口")
@RequiredArgsConstructor
@Slf4j
public class FileUploadController {

    private final FileUploadService fileUploadService;


    @PostMapping("/uploadOSS")
    @Operation(summary = "上传图片到OSS", description = "上传图片到OSS,model参数:模块名称（如: brand, activity, prize）")
    @NoLogin
    public Result<Map<String, Object>> uploadOSS(@RequestParam("photo") MultipartFile photo,
                                    @RequestParam("model") String model){
        Map<String, Object> resMap = fileUploadService.uploadImage(photo, model);
        return Result.success(resMap);
    }

    @PostMapping("/deleteOSS")
    @Operation(summary = "从OSS中删除图片", description = "从OSS中删除图片")
    @NoLogin
    public Result<String> deleteOSS(@RequestParam("fileUrl") String fileUrl){
        boolean b = fileUploadService.deleteFile(fileUrl);
        if (b){
            return Result.success("删除成功");
        }
        return Result.error("删除失败");
    }
}
