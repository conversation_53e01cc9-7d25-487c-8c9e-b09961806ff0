package yogu.pro.interceptors;


import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import yogu.pro.annotation.NoLogin;
import yogu.pro.constant.SystemConstant;
import yogu.pro.util.JwtUtil;
import yogu.pro.util.ThreadLocalUtil;

/**
 * <AUTHOR>
 * @date ：2024/3/8 20:46
 */

@Component
@Slf4j
public class LoginInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod handlerMethod){
            if (handlerMethod.getMethod().isAnnotationPresent(NoLogin.class) ||
                    handlerMethod.getBeanType().isAnnotationPresent(NoLogin.class)) {
                return true;
            }
        }

        //令牌验证
        String token = request.getHeader(SystemConstant.AUTHORIZATION);
        //验证token
        try {
            if (token == null) {
                response.setStatus(401);
                return false;
            }
            //从token中 获取信息
            Claims map = JwtUtil.parseJWT(token);
            String employeeId = MapUtils.getString(map, "phone_number");

            //把用户id存储到ThreadLocal中
            ThreadLocalUtil.set(employeeId);
            //放行
            return true;
        } catch (Exception e) {
            response.setStatus(401);
            //不放行
            return false;
        }

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        //清空ThreadLocal中的数据 防止内存泄漏
        ThreadLocalUtil.remove();
    }
}
