package yogu.pro.interceptors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import yogu.pro.util.Md5Util;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * API签名验证拦截器
 * 用于指定API调用的签名
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Component
@Slf4j
public class ApiSignatureInterceptor implements HandlerInterceptor {

    @Value("${api.auth.app-id}")
    private String appId;

    @Value("${api.auth.app-key}")
    private String appKey;

    @Value("${api.auth.enabled:true}")
    private boolean enabled;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果未启用签名验证，直接放行
        if (!enabled) {
            return true;
        }

        try {
            // 获取base64编码的请求体
            String base64Data = getRequestBody(request);
            if (base64Data.isBlank() || base64Data.trim().isEmpty()) {
                log.warn("API签名验证失败：请求体为空");
                response.setStatus(400);
                response.getWriter().write("{\"code\":400,\"message\":\"请求体不能为空\"}");
                return false;
            }

            // 解码base64数据
            String decodedData = decodeBase64(base64Data);
            log.debug("解码后的数据: {}", decodedData);

            // 解析JSON数据
            JsonNode requestNode = objectMapper.readTree(decodedData);
            
            // 验证必要字段
            if (!validateRequiredFields(requestNode)) {
                log.warn("API签名验证失败：缺少必要字段");
                response.setStatus(400);
                response.getWriter().write("{\"code\":400,\"message\":\"缺少必要字段\"}");
                return false;
            }

            // 验证签名
            if (!validateSignature(requestNode)) {
                log.warn("API签名验证失败：签名不匹配");
                response.setStatus(401);
                response.getWriter().write("{\"code\":401,\"message\":\"签名验证失败\"}");
                return false;
            }

            log.info("API签名验证通过");
            return true;

        } catch (Exception e) {
            log.error("API签名验证异常", e);
            response.setStatus(500);
            response.getWriter().write("{\"code\":500,\"message\":\"服务器内部错误\"}");
            return false;
        }
    }

    /**
     * 获取请求体
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        return StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
    }

    /**
     * 解码base64数据
     */
    private String decodeBase64(String base64Data) {
        try {
            // 处理可能的换行符和空格
            String cleanData = base64Data.trim().replace("\\r", " ").replace("\\n", " ");
            byte[] decodedBytes = Base64.getDecoder().decode(cleanData);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Base64解码失败", e);
            throw new RuntimeException("Base64解码失败", e);
        }
    }

    /**
     * 验证必要字段
     */
    private boolean validateRequiredFields(JsonNode requestNode) {
        JsonNode header = requestNode.get("header");
        JsonNode body = requestNode.get("body");
        
        if (header == null || body == null) {
            return false;
        }

        // 验证header中的必要字段
        if (header.get("appId") == null || 
            header.get("method") == null || 
            header.get("sign") == null) {
            return false;
        }

        return true;
    }

    /**
     * 验证签名
     */
    private boolean validateSignature(JsonNode requestNode) {
        try {
            JsonNode header = requestNode.get("header");
            JsonNode body = requestNode.get("body");
            
            String requestAppId = header.get("appId").asText();
            String requestMethod = header.get("method").asText();
            String requestSign = header.get("sign").asText();
            String requestBody = body.asText();

            // 验证appId
            if (!appId.equals(requestAppId)) {
                log.warn("API签名验证失败：appId不匹配，期望：{}，实际：{}", appId, requestAppId);
                return false;
            }

            // 计算签名
            String expectedSign = Md5Util.getMD5String(appId + requestBody + appKey).toUpperCase();
            
            // 验证签名
            if (!expectedSign.equals(requestSign)) {
                log.warn("API签名验证失败：签名不匹配，期望：{}，实际：{}", expectedSign, requestSign);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("API签名验证异常", e);
            return false;
        }
    }
} 