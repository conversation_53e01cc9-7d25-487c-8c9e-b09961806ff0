spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: text
      password: Heyin123
      # 连接池配置优化
      initial-size: 3
      min-idle: 3
      max-active: 15
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 180000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 性能优化配置
      break-after-acquire-failure: true
      connection-error-retry-attempts: 3
      query-timeout: 30
      transaction-query-timeout: 60

#     data:
#    redis:
#      host: *************
#      port: 6379
#      database: 0
#      lettuce:
#        pool:
#          max-active: 10
#          max-idle: 10
#          min-idle: 1
#          time-between-eviction-runs: 10s

# 阿里云OSS配置
aliyun:
  oss:
    # OSS配置
    endpoint: https://oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5tCTxGHB9zMsdZCmynF8
    access-key-secret: ******************************
    bucket-name: common-prize
    # 文件URL前缀（CDN域名或OSS域名）
    domain: https://common-prize.oss-cn-beijing.aliyuncs.com
    # 上传文件夹路径
    prefix: lottery-system/
    # 允许上传的文件类型
    allowed-extensions:
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - webp
    # 文件大小限制（MB）
    max-file-size: 5


# 配置 MyBatis-Plus
mybatis-plus:
  # 指定别名包路径，MyBatis-Plus会扫描该包下的类并注册为MyBatis的别名
  type-aliases-package: yogu.pro.pojo
  # Mapper.xml文件地址，当前这个是默认值。
  mapper-locations: classpath*:/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true #是否开启下划线和驼峰的映射
    cache-enabled: false # 是否开启二级缓存
  global-config:
    db-config:
      id-type: auto #设置id为自增长
      update-strategy: not_null #更新策略 只更新非空字段

# 配置日志
logging:
  # 配置日志级别
  level:
    # 指定yogu.pro包下的日志级别为debug
    yogu.pro: debug
  # 指定日志输出格式
  pattern:
    # 指定日期格式为HH:mm:ss
    dateformat: HH:mm:ss

#配置swagger信息：
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: yogu.pro.controller
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn

# API认证配置
api:
  auth:
    enabled: true
    app-id: appId001
    app-key: appKey001
