# Prize System - 抽奖活动管理系统

## 📋 项目概述

Prize System 是一个现代化的抽奖活动管理系统，采用前后端分离架构，提供完整的抽奖活动创建、管理和运营解决方案。系统支持多种抽奖形式，具备灵活的规则配置和丰富的管理功能。

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2.3
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.5
- **连接池**: Druid 1.2.21
- **缓存**: Redis (可选)
- **文档**: Knife4j (Swagger)
- **工具库**: Hutool 5.8.27
- **文件存储**: 阿里云 OSS
- **Java版本**: JDK 17

### 前端技术栈
- **框架**: Vue 3.4.38
- **构建工具**: Vite 5.4.2
- **语言**: TypeScript 5.5.3
- **路由**: Vue Router 4.3.0
- **样式**: Tailwind CSS 3.4.0
- **图标**: Lucide Vue Next 0.344.0
- **包管理**: pnpm

## 🎯 核心功能

### 1. 活动管理
- **活动创建**: 支持转盘抽奖、刮刮乐、老虎机等多种形式
- **活动配置**: 灵活设置活动时间、规则、奖品等
- **活动状态管理**: 未开始、进行中、已结束状态控制
- **活动列表**: 支持搜索、筛选、分页展示

### 2. 奖品管理
- **奖品配置**: 支持实物奖品、红包、谢谢参与等多种类型
- **概率设置**: 精确控制各奖品中奖概率
- **库存管理**: 实时监控奖品库存状态
- **奖品展示**: 支持图片上传和展示

### 3. 用户管理
- **用户信息**: 用户基础信息管理
- **参与记录**: 用户参与活动历史记录
- **中奖记录**: 用户中奖情况统计
- **黑名单管理**: 防止恶意参与

### 4. 数据统计
- **活动数据**: 参与人数、中奖人数等统计
- **趋势分析**: 活动参与趋势图表
- **奖品分布**: 各奖品中奖情况分析
- **实时监控**: 活动运行状态实时监控

### 5. 系统设置
- **UI配置**: 自定义抽奖界面样式
- **提示语设置**: 自定义各种场景提示语
- **权限控制**: 基于角色的访问控制
- **系统参数**: 全局系统参数配置

## 📊 数据库设计

### 核心数据表

1. **activity_base_info** - 活动基础信息表
   - 活动类型、名称、时间、规则等基础信息

2. **activity_draw_settings** - 抽奖规则设置表
   - 积分消耗、次数限制、城市限制等规则

3. **activity_prize_prob** - 奖品概率配置表
   - 奖品类型、名称、库存、概率等配置

4. **activity_prompt_ui** - 提示语和UI配置表
   - 各种提示语和界面样式配置

## 🚀 快速开始

### 环境要求
- JDK 17+
- Node.js 18+
- MySQL 8.0+
- Redis (可选)

### 后端启动
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 前端启动
```bash
cd frontend
pnpm install
pnpm dev
```

### 访问地址
- 前端管理界面: http://localhost:5173
- 后端API文档: http://localhost:8080/doc.html

## 📁 项目结构

```
prizes-backend/
├── backend/                    # 后端项目
│   ├── src/main/java/yogu/pro/
│   │   ├── controller/         # 控制器层
│   │   ├── service/           # 服务层
│   │   ├── mapper/            # 数据访问层
│   │   ├── pojo/              # 实体类
│   │   ├── config/            # 配置类
│   │   ├── util/              # 工具类
│   │   └── PrizeSystemBeApplication.java
│   └── src/main/resources/
│       ├── mapper/            # MyBatis映射文件
│       └── application-*.yaml # 配置文件
├── frontend/                   # 前端项目
│   ├── src/
│   │   ├── views/             # 页面组件
│   │   ├── components/        # 通用组件
│   │   ├── router/            # 路由配置
│   │   ├── utils/             # 工具函数
│   │   └── main.ts
│   ├── package.json
│   └── vite.config.ts
└── README.md
```

## 🔧 配置说明

### 数据库配置
在 `application-dev.yaml` 中配置数据库连接信息：
```yaml
spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
```

### 阿里云OSS配置
配置文件上传功能：
```yaml
aliyun:
  oss:
    endpoint: https://oss-cn-beijing.aliyuncs.com
    access-key-id: your_access_key
    access-key-secret: your_access_secret
    bucket-name: your_bucket_name
```

## 🎨 界面预览

### 主要页面
- **仪表板**: 数据概览和统计图表
- **活动管理**: 活动列表、创建、编辑
- **奖品管理**: 奖品配置和库存管理
- **用户管理**: 用户信息和参与记录
- **中奖记录**: 中奖情况统计和导出
- **系统设置**: 界面样式和提示语配置

## 🔒 安全特性

- **JWT认证**: 基于Token的身份验证
- **权限控制**: 基于角色的访问控制
- **数据验证**: 前后端双重数据验证
- **SQL注入防护**: 使用MyBatis-Plus防止SQL注入
- **XSS防护**: 输入输出过滤

## 📈 性能优化

- **数据库连接池**: 使用Druid连接池优化数据库连接
- **缓存机制**: Redis缓存热点数据
- **前端优化**: Vite构建工具，代码分割
- **图片优化**: 阿里云OSS CDN加速

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: mcayan
- 邮箱: [<EMAIL>]
- 项目地址: [https://github.com/your-username/prizes-backend]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！ 