# Cursor AI 开发规则 - 前端项目

## 项目概述

这是一个基于 Vue 3 + TypeScript + Vite 的抽奖管理系统前端项目。请遵循以下规则进行代码开发和修改。

## 技术栈规范

### 核心框架
- **Vue 3.4.38**: 使用 Composition API
- **TypeScript 5.5.3**: 严格类型检查
- **Vite 5.4.2**: 构建工具
- **Vue Router 4.3.0**: 路由管理
- **Tailwind CSS 3.4.0**: 样式框架
- **Lucide Vue Next 0.344.0**: 图标库

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查

## 代码风格规范

### 1. Vue 组件规范

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入语句
import { ref, computed } from 'vue'

// 类型定义
interface Props {
  title: string
  count?: number
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// 响应式数据
const loading = ref(false)

// 计算属性
const displayTitle = computed(() => `${props.title} (${props.count})`)

// 方法定义
const handleClick = () => {
  // 处理逻辑
}
</script>

<style scoped>
/* 组件样式 */
</style>
```

#### 命名规范
- **组件名**: PascalCase (`UserProfile.vue`)
- **文件名**: PascalCase (`ActivityList.vue`)
- **变量名**: camelCase (`userName`)
- **常量名**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **方法名**: camelCase (`handleSubmit`)

### 2. TypeScript 规范

#### 类型定义
```typescript
// 接口定义
interface User {
  id: number
  name: string
  email: string
  createdAt: Date
}

// 类型别名
type ApiResponse<T> = {
  code: number
  message: string
  data: T
}

// 枚举定义
enum ActivityStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ENDED = 'ended'
}
```

#### 严格类型检查
- 避免使用 `any` 类型
- 为所有变量提供明确的类型
- 使用泛型提高代码复用性
- 优先使用接口而非类型别名

### 3. 样式规范

#### Tailwind CSS 使用
```vue
<!-- 推荐: 使用 Tailwind 类名 -->
<div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
  <h2 class="text-lg font-semibold text-gray-900">标题</h2>
  <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
    按钮
  </button>
</div>

<!-- 避免: 内联样式 -->
<div style="display: flex; align-items: center; padding: 1rem;">
```

#### 响应式设计
```vue
<!-- 移动优先的响应式设计 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  <div class="p-4 bg-white rounded-lg">
    内容
  </div>
</div>
```

### 4. 路由规范

#### 路由配置
```typescript
// router/index.ts
const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: {
      title: '仪表盘',
      requiresAuth: true
    }
  }
]
```

#### 路由守卫
```typescript
// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 权限检查逻辑
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
  } else {
    next()
  }
})
```

## 开发最佳实践

### 1. 组件开发

#### 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 设计通用的组件接口
- **可测试性**: 组件逻辑清晰，易于测试
- **可维护性**: 代码结构清晰，注释完整

#### 组件通信
```vue
<!-- 父组件 -->
<template>
  <UserList 
    :users="users" 
    @user-selected="handleUserSelect"
    @user-deleted="handleUserDelete"
  />
</template>

<!-- 子组件 -->
<script setup lang="ts">
const emit = defineEmits<{
  userSelected: [user: User]
  userDeleted: [userId: number]
}>()

const handleClick = (user: User) => {
  emit('userSelected', user)
}
</script>
```

### 2. 状态管理

#### 本地状态
```typescript
// 使用 ref 和 reactive
const loading = ref(false)
const formData = reactive({
  name: '',
  email: '',
  phone: ''
})
```

#### 全局状态 (预留)
```typescript
// stores/user.ts (使用 Pinia)
export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  
  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }
  
  return {
    user,
    isAuthenticated,
    login
  }
})
```

### 3. API 调用

#### 请求封装
```typescript
// utils/request.ts
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
})

// 使用示例
const fetchUsers = async (): Promise<User[]> => {
  const response = await request.get<ApiResponse<User[]>>('/users')
  return response.data
}
```

#### 错误处理
```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    // 未授权，跳转登录
    router.push('/login')
  } else {
    // 显示错误消息
    ElMessage.error(error.message || '请求失败')
  }
}
```

### 4. 性能优化

#### 代码分割
```typescript
// 路由懒加载
const routes = [
  {
    path: '/activities',
    component: () => import('../views/ActivityList.vue')
  }
]
```

#### 组件优化
```vue
<!-- 使用 v-memo 优化列表渲染 -->
<template>
  <div v-for="item in items" :key="item.id" v-memo="[item.id, item.status]">
    {{ item.name }}
  </div>
</template>
```

## 文件组织规范

### 目录结构
```
src/
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── forms/          # 表单组件
│   └── layout/         # 布局组件
├── views/              # 页面组件
├── router/             # 路由配置
├── utils/              # 工具函数
├── api/                # API 接口
├── stores/             # 状态管理
├── types/              # 类型定义
├── config/             # 配置文件
└── assets/             # 静态资源
```

### 文件命名
- **组件文件**: `UserProfile.vue`
- **工具文件**: `dateUtils.ts`
- **类型文件**: `user.types.ts`
- **API文件**: `userApi.ts`

## 代码质量要求

### 1. 代码审查清单
- [ ] TypeScript 类型检查通过
- [ ] ESLint 检查通过
- [ ] 组件命名符合规范
- [ ] 代码注释完整
- [ ] 错误处理完善
- [ ] 响应式设计支持

### 2. 测试要求
- 组件单元测试覆盖率 > 80%
- 关键业务逻辑测试
- 用户交互测试

### 3. 性能要求
- 首屏加载时间 < 2s
- 组件渲染时间 < 16ms
- 包体积优化

## 安全规范

### 1. 输入验证
```typescript
// 表单验证
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
```

### 2. XSS 防护
```vue
<!-- 使用 v-text 或 v-html 时注意转义 -->
<template>
  <div v-text="userInput"></div>
  <!-- 避免直接使用 v-html -->
</template>
```

### 3. 敏感信息处理
```typescript
// 环境变量管理
const apiKey = import.meta.env.VITE_API_KEY
// 避免在代码中硬编码敏感信息
```

## 部署规范

### 1. 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          utils: ['axios', 'lodash']
        }
      }
    }
  }
})
```

### 2. 环境配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_DEBUG=true

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_DEBUG=false
```

## 常见问题解决

### 1. TypeScript 错误
- 检查类型定义是否完整
- 使用类型断言时谨慎
- 优先使用接口而非类型别名

### 2. 样式问题
- 检查 Tailwind CSS 类名是否正确
- 使用浏览器开发者工具调试
- 注意 CSS 优先级

### 3. 路由问题
- 检查路由配置是否正确
- 确认组件导入路径
- 验证路由守卫逻辑

## 更新日志

- **2024-01**: 初始版本，基于 Vue 3 + TypeScript + Vite
- **2024-02**: 添加 Tailwind CSS 和 Lucide 图标
- **2024-03**: 完善组件架构和路由配置

---

*本规则文档会随着项目发展持续更新* 