<template>
  <div class="relative">
    <button
      ref="trigger"
      type="button"
      :class="cn(
        'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        $attrs.class
      )"
      :disabled="$attrs.disabled"
      @click="toggle"
      @blur="handleBlur"
    >
      <span :class="{ 'text-muted-foreground': !modelValue }">
        {{ displayValue || placeholder || '请选择...' }}
      </span>
      <ChevronDown 
        class="h-4 w-4 opacity-50 transition-transform duration-200"
        :class="{ 'rotate-180': isOpen }"
      />
    </button>
    
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="isOpen"
        ref="content"
        class="absolute z-50 w-full min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95"
        :style="{ top: '100%', marginTop: '2px' }"
      >
        <div class="max-h-60 overflow-auto">
          <slot :selected-value="modelValue" :on-select="handleSelect" />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, provide } from 'vue'
import { ChevronDown } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

interface Props {
  modelValue?: string | number
  placeholder?: string
  displayValue?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择...'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

const trigger = ref<HTMLButtonElement>()
const content = ref<HTMLDivElement>()
const isOpen = ref(false)

const displayValue = computed(() => {
  if (props.displayValue) return props.displayValue
  if (!props.modelValue) return ''
  return props.modelValue
})

const toggle = () => {
  isOpen.value = !isOpen.value
}

const handleSelect = (value: string | number) => {
  emit('update:modelValue', value)
  isOpen.value = false
}

const handleBlur = (event: FocusEvent) => {
  // 延迟关闭，确保点击选项时能正确触发
  setTimeout(() => {
    if (!content.value?.contains(event.relatedTarget as Node)) {
      isOpen.value = false
    }
  }, 100)
}

const handleClickOutside = (event: Event) => {
  if (trigger.value && !trigger.value.contains(event.target as Node) && 
      content.value && !content.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

// 提供选中值和选择函数给子组件
provide('selectedValue', computed(() => props.modelValue))
provide('onSelect', handleSelect)

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 暴露方法给父组件
defineExpose({
  isOpen: computed(() => isOpen.value),
  open: () => { isOpen.value = true },
  close: () => { isOpen.value = false }
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}
</style>
