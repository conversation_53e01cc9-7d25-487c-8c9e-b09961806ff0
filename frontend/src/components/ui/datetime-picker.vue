<template>
  <div>
    <Button
      variant="outline"
      :class="cn(
        'w-full justify-start text-left font-normal',
        !date && 'text-muted-foreground'
      )"
    >
      <CalendarIcon class="mr-2 h-4 w-4" />
      {{ date ? format(date, 'PPP p') : 'Pick a date' }}
    </Button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { Button } from '@/components/ui'
import { cn } from '@/lib/utils'

interface Props {
  modelValue?: Date
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined
})

const emit = defineEmits<{
  'update:modelValue': [value: Date | undefined]
}>()

const date = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const hours = ref(props.modelValue?.getHours() || 0)
const minutes = ref(props.modelValue?.getMinutes() || 0)
</script>
