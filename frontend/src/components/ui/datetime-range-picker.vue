<template>
  <div class="relative">
    <Input
      :value="displayValue"
      :placeholder="placeholder"
      readonly
      @click="showPicker = true"
      :class="cn('cursor-pointer', $attrs.class)"
    />
    <Calendar class="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none" />
    
    <!-- 日期时间范围选择弹窗 -->
    <div v-if="showPicker" class="absolute z-50 mt-1 w-full">
      <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
        <!-- 日期选择部分 -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-4">
            <button
              @click="previousMonth"
              class="p-1 hover:bg-gray-100 rounded"
            >
              ‹
            </button>
            <span class="font-medium">{{ currentMonthYear }}</span>
            <button
              @click="nextMonth"
              class="p-1 hover:bg-gray-100 rounded"
            >
              ›
            </button>
          </div>
          
          <!-- 星期标题 -->
          <div class="grid grid-cols-7 gap-1 mb-2">
            <div v-for="day in weekDays" :key="day" class="text-center text-xs font-medium text-gray-500 py-1">
              {{ day }}
            </div>
          </div>
          
          <!-- 日期网格 -->
          <div class="grid grid-cols-7 gap-1">
            <button
              v-for="date in calendarDates"
              :key="date.key"
              @click="selectDate(date)"
              :class="cn(
                'h-8 w-full text-sm rounded hover:bg-gray-100 transition-colors',
                date.isCurrentMonth ? 'text-gray-900' : 'text-gray-400',
                date.isInRange ? 'bg-gray-200' : '',
                date.isStartDate ? 'bg-blue-600 text-white hover:bg-blue-700' : '',
                date.isEndDate ? 'bg-blue-600 text-white hover:bg-blue-700' : '',
                date.isToday ? 'font-bold' : ''
              )"
              :disabled="!date.isCurrentMonth"
            >
              {{ date.day }}
            </button>
          </div>
        </div>
        
        <!-- 时间选择部分 -->
        <div class="border-t pt-4 mb-4">
          <div class="text-sm font-medium text-gray-700 mb-2">选择时间</div>
          <div class="grid grid-cols-2 gap-4">
            <!-- 开始时间 -->
            <div>
              <div class="text-xs text-gray-500 mb-1">开始时间</div>
              <div class="grid grid-cols-3 gap-1">
                <Select v-model="startHour" placeholder="时" class="w-full">
                  <SelectOption v-for="hour in 24" :key="hour" :value="hour - 1">
                    {{ String(hour - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
                <Select v-model="startMinute" placeholder="分" class="w-full">
                  <SelectOption v-for="minute in 60" :key="minute" :value="minute - 1">
                    {{ String(minute - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
                <Select v-model="startSecond" placeholder="秒" class="w-full">
                  <SelectOption v-for="second in 60" :key="second" :value="second - 1">
                    {{ String(second - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
              </div>
            </div>
            <!-- 结束时间 -->
            <div>
              <div class="text-xs text-gray-500 mb-1">结束时间</div>
              <div class="grid grid-cols-3 gap-1">
                <Select v-model="endHour" placeholder="时" class="w-full">
                  <SelectOption v-for="hour in 24" :key="hour" :value="hour - 1">
                    {{ String(hour - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
                <Select v-model="endMinute" placeholder="分" class="w-full">
                  <SelectOption v-for="minute in 60" :key="minute" :value="minute - 1">
                    {{ String(minute - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
                <Select v-model="endSecond" placeholder="秒" class="w-full">
                  <SelectOption v-for="second in 60" :key="second" :value="second - 1">
                    {{ String(second - 1).padStart(2, '0') }}
                  </SelectOption>
                </Select>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 选择状态显示 -->
        <div class="border-t pt-4 mb-4">
          <div class="text-sm text-gray-600 mb-2">
            <div v-if="!startDate && !endDate">请选择开始日期</div>
            <div v-else-if="startDate && !endDate">请选择结束日期</div>
            <div v-else>已选择: {{ formatDateTimeRange() }}</div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-between pt-4 border-t">
          <Button variant="outline" size="sm" @click="clearRange">
            清除
          </Button>
          <Button size="sm" @click="confirmRange">
            确定
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, startOfWeek, endOfWeek, isWithinInterval, setHours, setMinutes, setSeconds } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { Calendar } from 'lucide-vue-next'
import { Input, Button, Select, SelectOption } from '@/components/ui'
import { cn } from '@/lib/utils'

interface Props {
  modelValue?: { startDateTime?: Date; endDateTime?: Date }
  placeholder?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  placeholder: '选择活动时间',
  class: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: { startDateTime?: Date; endDateTime?: Date }]
}>()

const showPicker = ref(false)
const currentDate = ref(new Date())
const startDate = ref<Date | undefined>(props.modelValue?.startDateTime)
const endDate = ref<Date | undefined>(props.modelValue?.endDateTime)

// 时间选择
const startHour = ref<number>(props.modelValue?.startDateTime?.getHours() ?? 0)
const startMinute = ref<number>(props.modelValue?.startDateTime?.getMinutes() ?? 0)
const startSecond = ref<number>(props.modelValue?.startDateTime?.getSeconds() ?? 0)
const endHour = ref<number>(props.modelValue?.endDateTime?.getHours() ?? 23)
const endMinute = ref<number>(props.modelValue?.endDateTime?.getMinutes() ?? 59)
const endSecond = ref<number>(props.modelValue?.endDateTime?.getSeconds() ?? 59)

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

const displayValue = computed(() => {
  if (!startDate.value && !endDate.value) return ''
  if (startDate.value && !endDate.value) {
    const startDateTime = setSeconds(setMinutes(setHours(startDate.value, startHour.value), startMinute.value), startSecond.value)
    return format(startDateTime, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
  }
  if (startDate.value && endDate.value) {
    const startDateTime = setSeconds(setMinutes(setHours(startDate.value, startHour.value), startMinute.value), startSecond.value)
    const endDateTime = setSeconds(setMinutes(setHours(endDate.value, endHour.value), endMinute.value), endSecond.value)
    return `${format(startDateTime, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })} 至 ${format(endDateTime, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}`
  }
  return ''
})

const currentMonthYear = computed(() => {
  return format(currentDate.value, 'yyyy年MM月', { locale: zhCN })
})

const calendarDates = computed(() => {
  const start = startOfWeek(startOfMonth(currentDate.value), { locale: zhCN })
  const end = endOfWeek(endOfMonth(currentDate.value), { locale: zhCN })
  const days = eachDayOfInterval({ start, end })
  
  return days.map(day => ({
    key: format(day, 'yyyy-MM-dd'),
    day: format(day, 'd'),
    date: day,
    isCurrentMonth: day.getMonth() === currentDate.value.getMonth(),
    isStartDate: startDate.value ? isSameDay(day, startDate.value) : false,
    isEndDate: endDate.value ? isSameDay(day, endDate.value) : false,
    isInRange: startDate.value && endDate.value ? isWithinInterval(day, { start: startDate.value, end: endDate.value }) : false,
    isToday: isToday(day)
  }))
})

const previousMonth = () => {
  currentDate.value = subMonths(currentDate.value, 1)
}

const nextMonth = () => {
  currentDate.value = addMonths(currentDate.value, 1)
}

const selectDate = (date: any) => {
  if (!date.isCurrentMonth) return
  
  if (!startDate.value || (startDate.value && endDate.value)) {
    // 开始新的选择
    startDate.value = date.date
    endDate.value = undefined
  } else {
    // 选择结束日期
    if (date.date < startDate.value!) {
      // 如果选择的日期早于开始日期，交换位置
      endDate.value = startDate.value
      startDate.value = date.date
    } else {
      endDate.value = date.date
    }
  }
}

const confirmRange = () => {
  let startDateTime: Date | undefined
  let endDateTime: Date | undefined
  
  if (startDate.value) {
    startDateTime = setSeconds(setMinutes(setHours(startDate.value, startHour.value), startMinute.value), startSecond.value)
  }
  
  if (endDate.value) {
    endDateTime = setSeconds(setMinutes(setHours(endDate.value, endHour.value), endMinute.value), endSecond.value)
  }
  
  emit('update:modelValue', { startDateTime, endDateTime })
  showPicker.value = false
}

const clearRange = () => {
  startDate.value = undefined
  endDate.value = undefined
  startHour.value = 0
  startMinute.value = 0
  startSecond.value = 0
  endHour.value = 23
  endMinute.value = 59
  endSecond.value = 59
  emit('update:modelValue', { startDateTime: undefined, endDateTime: undefined })
}

// 监听props变化，更新内部状态
watch(() => props.modelValue, (newValue) => {
  if (newValue?.startDateTime) {
    startDate.value = newValue.startDateTime
    startHour.value = newValue.startDateTime.getHours()
    startMinute.value = newValue.startDateTime.getMinutes()
    startSecond.value = newValue.startDateTime.getSeconds()
  }
  if (newValue?.endDateTime) {
    endDate.value = newValue.endDateTime
    endHour.value = newValue.endDateTime.getHours()
    endMinute.value = newValue.endDateTime.getMinutes()
    endSecond.value = newValue.endDateTime.getSeconds()
  }
}, { immediate: true })

const formatDateTimeRange = () => {
  if (startDate.value && endDate.value) {
    const startDateTime = setSeconds(setMinutes(setHours(startDate.value, startHour.value), startMinute.value), startSecond.value)
    const endDateTime = setSeconds(setMinutes(setHours(endDate.value, endHour.value), endMinute.value), endSecond.value)
    return `${format(startDateTime, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })} 至 ${format(endDateTime, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}`
  }
  return ''
}

const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showPicker.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    startDate.value = newValue.startDateTime
    endDate.value = newValue.endDateTime
    if (newValue.startDateTime) {
      startHour.value = newValue.startDateTime.getHours()
      startMinute.value = newValue.startDateTime.getMinutes()
      startSecond.value = newValue.startDateTime.getSeconds()
      currentDate.value = newValue.startDateTime
    }
    if (newValue.endDateTime) {
      endHour.value = newValue.endDateTime.getHours()
      endMinute.value = newValue.endDateTime.getMinutes()
      endSecond.value = newValue.endDateTime.getSeconds()
    }
  }
})
</script> 