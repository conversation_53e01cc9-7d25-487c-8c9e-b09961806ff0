<template>
  <div class="rich-text-editor">
    <!-- 工具栏 -->
    <div class="toolbar border border-border rounded-t-lg bg-muted/50 p-2">
      <div class="flex items-center space-x-1">
        <!-- 基础格式工具 -->
        <button
          v-for="tool in basicTools"
          :key="tool.name"
          @click="tool.action"
          :class="[
            'px-2 py-1 rounded text-xs font-medium transition-colors',
            tool.isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-accent hover:text-accent-foreground text-foreground'
          ]"
          :title="tool.title"
          type="button"
        >
          {{ tool.label }}
        </button>
        
        <div class="w-px h-6 bg-border mx-1"></div>
        
        <!-- 列表工具 -->
        <button
          v-for="tool in listTools"
          :key="tool.name"
          @click="tool.action"
          :class="[
            'p-2 rounded transition-colors',
            tool.isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-accent hover:text-accent-foreground text-foreground'
          ]"
          :title="tool.title"
          type="button"
        >
          <component :is="tool.icon" class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-border mx-1"></div>
        
        <!-- 对齐工具 -->
        <button
          v-for="tool in alignTools"
          :key="tool.name"
          @click="tool.action"
          :class="[
            'p-2 rounded transition-colors',
            tool.isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-accent hover:text-accent-foreground text-foreground'
          ]"
          :title="tool.title"
          type="button"
        >
          <component :is="tool.icon" class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-border mx-1"></div>
        
        <!-- 插入工具 -->
        <button
          @click="insertImage"
          class="p-2 rounded transition-colors hover:bg-accent hover:text-accent-foreground text-foreground"
          type="button"
        >
          <Target class="w-4 h-4" />
        </button>
        <button
          @click="insertLink"
          class="p-2 rounded transition-colors hover:bg-accent hover:text-accent-foreground text-foreground"
          type="button"
        >
          <MessageSquare class="w-4 h-4" />
        </button>
        <button
          @click="insertTable"
          class="p-2 rounded transition-colors hover:bg-accent hover:text-accent-foreground text-foreground"
          type="button"
        >
          <Palette class="w-4 h-4" />
        </button>
        
        <div class="w-px h-6 bg-border mx-1"></div>
        
        <!-- 字体设置 -->
        <Select
          v-model="selectedFontSize"
          @update:model-value="setFontSize"
          placeholder="字体大小"
          class="w-20"
        >
          <SelectOption value="12px">12px</SelectOption>
          <SelectOption value="14px">14px</SelectOption>
          <SelectOption value="16px">16px</SelectOption>
          <SelectOption value="18px">18px</SelectOption>
          <SelectOption value="20px">20px</SelectOption>
          <SelectOption value="24px">24px</SelectOption>
        </Select>
        <Select
          v-model="selectedFontFamily"
          @update:model-value="setFontFamily"
          placeholder="字体类型"
          class="w-28"
        >
          <SelectOption value="Arial">Arial</SelectOption>
          <SelectOption value="Times New Roman">Times New Roman</SelectOption>
          <SelectOption value="Courier New">Courier New</SelectOption>
          <SelectOption value="Verdana">Verdana</SelectOption>
        </Select>
        
        <div class="w-px h-6 bg-border mx-1"></div>
        
        <!-- 颜色选择器 -->
        <input
          type="color"
          @change="setTextColor"
          class="w-8 h-6 border border-border rounded cursor-pointer"
          title="文字颜色"
        />
        <input
          type="color"
          @change="setBackgroundColor"
          class="w-8 h-6 border border-border rounded cursor-pointer"
          title="背景颜色"
        />
        
        <div class="flex-1"></div>
        
        <!-- 字符计数 -->
        <div class="text-xs text-muted-foreground">
          {{ characterCount }}/{{ maxLength }}
        </div>
      </div>
    </div>
    
    <!-- 编辑区域 -->
    <div
      ref="editorRef"
      class="editor-content border border-border rounded-b-lg p-4 min-h-[200px] focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-background text-foreground"
      contenteditable="true"
      @input="handleInput"
      @keydown="handleKeydown"
      @paste="handlePaste"
      v-html="modelValue"
    ></div>
    
    <!-- 图片上传对话框 -->
    <div v-if="showImageDialog" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-background border border-border rounded-lg p-6 w-96 shadow-lg">
        <h3 class="text-lg font-semibold mb-4 text-foreground">插入图片</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-foreground mb-2">选择图片</label>
            <input
              type="file"
              accept="image/*"
              @change="handleImageSelect"
              class="w-full border border-input bg-background text-foreground rounded-md px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            />
          </div>
          <div v-if="imagePreview" class="text-center">
            <img :src="imagePreview" alt="预览" class="max-w-full h-32 object-contain mx-auto rounded-md border border-border" />
          </div>
          <div class="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              @click="cancelImageInsert"
            >
              取消
            </Button>
            <Button
              size="sm"
              @click="confirmImageInsert"
              :disabled="!selectedImage"
            >
              插入
            </Button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 链接插入对话框 -->
    <div v-if="showLinkDialog" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-background border border-border rounded-lg p-6 w-96 shadow-lg">
        <h3 class="text-lg font-semibold mb-4 text-foreground">插入链接</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-foreground mb-2">链接地址</label>
            <Input
              v-model="linkUrl"
              type="url"
              placeholder="https://example.com"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-foreground mb-2">链接文本</label>
            <Input
              v-model="linkText"
              type="text"
              placeholder="链接文本"
            />
          </div>
          <div class="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              @click="cancelLinkInsert"
            >
              取消
            </Button>
            <Button
              size="sm"
              @click="confirmLinkInsert"
              :disabled="!linkUrl"
            >
              插入
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Select, SelectOption, Button, Input } from '@/components/ui'
import { 
  List, Plus, Gift, ChevronRight, Settings, Users, 
  Eye, User, Award, X, Palette, MessageSquare, Target,
  Activity, TrendingUp, PieChart, Calendar
} from 'lucide-vue-next'

interface Props {
  modelValue: string
  placeholder?: string
  maxLength?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容...',
  maxLength: 2000
})

const emit = defineEmits<Emits>()

const editorRef = ref<HTMLElement>()
const isBold = ref(false)
const isItalic = ref(false)
const isUnderline = ref(false)
const selectedFontSize = ref('16px')
const selectedFontFamily = ref('Arial')
const showImageDialog = ref(false)
const showLinkDialog = ref(false)
const selectedImage = ref<File | null>(null)
const imagePreview = ref('')
const linkUrl = ref('')
const linkText = ref('')

const characterCount = computed(() => {
  return props.modelValue.replace(/<[^>]*>/g, '').length
})

const basicTools = computed(() => [
  {
    name: 'bold',
    label: 'B',
    title: '粗体 (Ctrl+B)',
    isActive: isBold.value,
    action: () => document.execCommand('bold', false)
  },
  {
    name: 'italic',
    label: 'I',
    title: '斜体 (Ctrl+I)',
    isActive: isItalic.value,
    action: () => document.execCommand('italic', false)
  },
  {
    name: 'underline',
    label: 'U',
    title: '下划线 (Ctrl+U)',
    isActive: isUnderline.value,
    action: () => document.execCommand('underline', false)
  },
  {
    name: 'strikethrough',
    label: 'S',
    title: '删除线',
    isActive: false,
    action: () => document.execCommand('strikethrough', false)
  }
])

const listTools = computed(() => [
  {
    name: 'unorderedList',
    label: '•',
    icon: List,
    title: '',
    isActive: false,
    action: () => document.execCommand('insertUnorderedList', false)
  },
  {
    name: 'orderedList',
    label: '1.',
    icon: Plus,
    title: '',
    isActive: false,
    action: () => document.execCommand('insertOrderedList', false)
  }
])

const alignTools = computed(() => [
  {
    name: 'alignLeft',
    label: '左',
    icon: Activity,
    title: '',
    isActive: false,
    action: () => document.execCommand('justifyLeft', false)
  },
  {
    name: 'alignCenter',
    label: '中',
    icon: TrendingUp,
    title: '',
    isActive: false,
    action: () => document.execCommand('justifyCenter', false)
  },
  {
    name: 'alignRight',
    label: '右',
    icon: PieChart,
    title: '',
    isActive: false,
    action: () => document.execCommand('justifyRight', false)
  },
  {
    name: 'justify',
    label: '两端',
    icon: Calendar,
    title: '',
    isActive: false,
    action: () => document.execCommand('justifyFull', false)
  }
])

const handleInput = () => {
  if (editorRef.value) {
    const content = editorRef.value.innerHTML
    if (characterCount.value <= props.maxLength) {
      emit('update:modelValue', content)
    }
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  // 检查字符数限制
  if (characterCount.value >= props.maxLength && 
      !event.ctrlKey && 
      !event.metaKey && 
      !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
    event.preventDefault()
  }
  
  // 快捷键支持
  if (event.ctrlKey || event.metaKey) {
    switch (event.key.toLowerCase()) {
      case 'b':
        event.preventDefault()
        document.execCommand('bold', false)
        break
      case 'i':
        event.preventDefault()
        document.execCommand('italic', false)
        break
      case 'u':
        event.preventDefault()
        document.execCommand('underline', false)
        break
    }
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  
  if (characterCount.value + text.length <= props.maxLength) {
    document.execCommand('insertText', false, text)
  }
}

const setFontSize = () => {
  document.execCommand('fontSize', false, '7')
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    const span = document.createElement('span')
    span.style.fontSize = selectedFontSize.value
    range.surroundContents(span)
  }
}

const setFontFamily = () => {
  document.execCommand('fontName', false, selectedFontFamily.value)
}

const setTextColor = (event: Event) => {
  const color = (event.target as HTMLInputElement).value
  document.execCommand('foreColor', false, color)
}

const setBackgroundColor = (event: Event) => {
  const color = (event.target as HTMLInputElement).value
  document.execCommand('hiliteColor', false, color)
}

const insertImage = () => {
  showImageDialog.value = true
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    const file = files[0]
    if (file.size > 5 * 1024 * 1024) {
      alert('图片大小不能超过5MB')
      return
    }
    
    selectedImage.value = file
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const confirmImageInsert = () => {
  if (selectedImage.value && imagePreview.value) {
    const img = document.createElement('img')
    img.src = imagePreview.value
    img.style.maxWidth = '100%'
    img.style.height = 'auto'
    
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.insertNode(img)
      range.collapse(false)
    }
    
    handleInput()
  }
  cancelImageInsert()
}

const cancelImageInsert = () => {
  showImageDialog.value = false
  selectedImage.value = null
  imagePreview.value = ''
}

const insertLink = () => {
  const selection = window.getSelection()
  if (selection && selection.toString()) {
    linkText.value = selection.toString()
  }
  showLinkDialog.value = true
}

const confirmLinkInsert = () => {
  if (linkUrl.value) {
    const link = document.createElement('a')
    link.href = linkUrl.value
    link.textContent = linkText.value || linkUrl.value
    link.target = '_blank'
    
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      if (linkText.value) {
        range.deleteContents()
      }
      range.insertNode(link)
      range.collapse(false)
    }
    
    handleInput()
  }
  cancelLinkInsert()
}

const cancelLinkInsert = () => {
  showLinkDialog.value = false
  linkUrl.value = ''
  linkText.value = ''
}

const insertTable = () => {
  const table = document.createElement('table')
  table.style.borderCollapse = 'collapse'
  table.style.width = '100%'
  table.style.marginBottom = '1rem'
  
  for (let i = 0; i < 3; i++) {
    const row = document.createElement('tr')
    for (let j = 0; j < 3; j++) {
      const cell = document.createElement('td')
      cell.style.border = '1px solid #ccc'
      cell.style.padding = '8px'
      cell.textContent = '单元格'
      row.appendChild(cell)
    }
    table.appendChild(row)
  }
  
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.insertNode(table)
    range.collapse(false)
  }
  
  handleInput()
}

const updateToolbarState = () => {
  if (editorRef.value) {
    isBold.value = document.queryCommandState('bold')
    isItalic.value = document.queryCommandState('italic')
    isUnderline.value = document.queryCommandState('underline')
  }
}

// 监听编辑器焦点和选择变化
watch(() => editorRef.value, (el) => {
  if (el) {
    el.addEventListener('focus', updateToolbarState)
    el.addEventListener('blur', updateToolbarState)
    el.addEventListener('mouseup', updateToolbarState)
    el.addEventListener('keyup', updateToolbarState)
  }
})

// 设置占位符
watch(() => props.modelValue, (newValue) => {
  nextTick(() => {
    if (editorRef.value && !newValue) {
      editorRef.value.innerHTML = ''
    }
  })
})
</script>

<style scoped>
.rich-text-editor {
  @apply w-full;
}

.editor-content {
  @apply text-sm leading-relaxed;
}

.editor-content:empty:before {
  content: attr(placeholder);
  @apply text-gray-400 pointer-events-none;
}

.editor-content:focus {
  @apply outline-none;
}

/* 富文本样式 */
.editor-content :deep(p) {
  @apply mb-2;
}

.editor-content :deep(ul), .editor-content :deep(ol) {
  @apply pl-6 mb-2;
}

.editor-content :deep(li) {
  @apply mb-1;
}

.editor-content :deep(blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600;
}

.editor-content :deep(strong) {
  @apply font-bold;
}

.editor-content :deep(em) {
  @apply italic;
}

.editor-content :deep(u) {
  @apply underline;
}

.editor-content :deep(s) {
  @apply line-through;
}

.editor-content :deep(img) {
  @apply max-w-full h-auto rounded;
}

.editor-content :deep(table) {
  @apply border-collapse w-full mb-4;
}

.editor-content :deep(td), .editor-content :deep(th) {
  @apply border border-gray-300 p-2;
}

.editor-content :deep(a) {
  @apply text-blue-600 hover:text-blue-800 underline;
}
</style> 