<template>
  <div
    :class="cn(
      'relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      {
        'bg-primary text-primary-foreground': isSelected
      }
    )"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, type Ref } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  value: string | number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

// 从父组件注入选中值和选择函数
const selectedValue = inject<Ref<string | number | undefined>>('selectedValue', ref(undefined))
const onSelect = inject<(value: string | number) => void>('onSelect', () => {})

const isSelected = computed(() => selectedValue.value === props.value)

const handleClick = () => {
  if (!props.disabled) {
    onSelect(props.value)
  }
}

const handleMouseEnter = () => {
  // 可以添加悬停效果
}

const handleMouseLeave = () => {
  // 可以添加离开效果
}
</script> 