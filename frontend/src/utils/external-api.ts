import CryptoJS from 'crypto-js'

// API响应类型
interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
}

// 用户资格验证响应
interface EligibilityResponse {
  eligible: boolean
  reason: string
  activityName: string
  activityType: number
  userDailyLimit: number
  userTotalLimit: number
  remainingChances: number
}

// 抽奖结果响应
interface LotteryResult {
  result: 'winning' | 'not_winning'
  message: string
  prizeInfo?: {
    prizeId: number
    prizeName: string
    prizeType: string
    prizeValue: string
    prizeImage: string
    prizeDescription: string
  }
  participationId: string
}

// 活动信息响应
interface ActivityInfo {
  id: number
  brandId: number
  brandName: string
  activityName: string
  activityType: number
  description: string
  startTime: string
  endTime: string
  dailyLimit: number
  totalLimit: number
  maxParticipants: number
  currentParticipants: number
  userDailyLimit: number
  userTotalLimit: number
  participationCount: number
  winningCount: number
  rules: string
  status: number
  createTime: string
  updateTime: string
}

// 中奖记录响应
interface WinningRecord {
  participationId: string
  activityId: number
  prizeId: number
  prizeName: string
  winningTime: string
  distributionStatus: number
  distributionTime: string | null
  logisticsInfo: string | null
}

// 剩余抽奖次数响应
interface RemainingAttemptsResponse {
  remainingAttempts: number    // 剩余抽奖次数
  todayParticipationCount: number    // 今日已参与次数
  totalParticipationCount: number    // 总参与次数
  userDailyLimit: number    // 每日限制
  userTotalLimit: number    // 总限制
}

/**
 * 抽奖系统外部API客户端
 * 实现HMAC-SHA256签名验证和完整的API调用
 */
export class ExternalLotteryAPI {
  private appKey: string
  private appSecret: string
  private baseURL: string

  constructor(
    appKey: string = 'demo_app_12345',
    appSecret: string = 'demo_secret_67890', 
    baseURL: string = ''  // 修改为空字符串，使用相对路径
  ) {
    this.appKey = appKey
    this.appSecret = appSecret
    this.baseURL = baseURL
  }

  /**
   * 生成随机字符串
   */
  private generateNonce(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 生成时间戳（秒级）
   */
  private generateTimestamp(): string {
    return Math.floor(Date.now() / 1000).toString()
  }

  /**
   * 构建签名字符串
   */
  private buildSignString(params: Record<string, any>): string {
    // 过滤空值参数
    const filteredParams = Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .reduce((obj, key) => {
        obj[key] = params[key]
        return obj
      }, {} as Record<string, any>)

    // 按key的ASCII码升序排列
    const sortedKeys = Object.keys(filteredParams).sort()
    
    // 拼接参数
    const paramString = sortedKeys
      .map(key => `${key}=${filteredParams[key]}`)
      .join('&')
    
    // 添加密钥
    return `${paramString}&key=${this.appSecret}`
  }

  /**
   * 生成HMAC-SHA256签名
   */
  private generateSign(params: Record<string, any>): string {
    const signString = this.buildSignString(params)
    return CryptoJS.HmacSHA256(signString, this.appSecret).toString().toUpperCase()
  }

  /**
   * 发起API请求
   */
  private async request<T = any>(endpoint: string, params: Record<string, any> = {}, method: 'GET' | 'POST' = 'POST'): Promise<ApiResponse<T>> {
    const timestamp = this.generateTimestamp()
    const nonce = this.generateNonce()
    
    // 构建签名参数（包含header参数和body/query参数）
    const signParams = {
      app_key: this.appKey,
      timestamp: timestamp,
      nonce: nonce,
      ...params
    }
    
    // 生成签名
    const sign = this.generateSign(signParams)
    
    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'X-App-Key': this.appKey,
      'X-Timestamp': timestamp,
      'X-Nonce': nonce,
      'X-Sign-Type': 'HMAC-SHA256',
      'X-Sign': sign
    }
    
    try {
      let url = `${this.baseURL}${endpoint}`
      let requestParams = params
      
      // 如果是GET请求，将参数添加到URL
      if (method === 'GET') {
        const queryString = Object.entries(requestParams)
          .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
          .join('&')
        url = `${url}?${queryString}`
        requestParams = {} // GET请求不需要body
      }
      
      const response = await fetch(url, {
        method,
        headers,
        body: method === 'POST' ? JSON.stringify(requestParams) : undefined
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `HTTP ${response.status}`)
      }
      
      const result = await response.json() as ApiResponse<T>
      return result
      
    } catch (error) {
      console.error('API请求失败:', error)
      throw error
    }
  }

  /**
   * 验证用户参与资格
   */
  async verifyEligibility(activityId: number, phone: string): Promise<ApiResponse<EligibilityResponse>> {
    return this.request<EligibilityResponse>('/api/external/verify/eligibility', {
      activityId: activityId,
      phone: phone
    }, 'POST')
  }

  /**
   * 参与抽奖
   */
  async drawLottery(params: {
    activityId: number
    userId: string
    phone: string
    name: string
    email?: string
  }): Promise<ApiResponse<LotteryResult>> {
    return this.request<LotteryResult>('/api/external/lottery/draw', {
      activityId: params.activityId,
      userId: params.userId,
      phone: params.phone,
      name: params.name,
      email: params.email
    })
  }

  /**
   * 查询抽奖结果
   */
  async getLotteryResults(userId: string, activityId: number): Promise<ApiResponse<WinningRecord[]>> {
    return this.request<WinningRecord[]>('/api/external/lottery/results', {
      userId: userId,
      activityId: activityId
    })
  }

  /**
   * 获取活动信息
   */
  async getActivityInfo(activityId: number): Promise<ApiResponse<ActivityInfo>> {
    return this.request<ActivityInfo>('/api/external/activity/info', {
      activityId: activityId
    })
  }

  /**
   * 获取剩余抽奖次数
   */
  async getRemainingAttempts(activityId: number, phone: string): Promise<ApiResponse<RemainingAttemptsResponse>> {
    return this.request<RemainingAttemptsResponse>('/api/external/lottery/remaining-attempts', {
      activityId: activityId,
      phone: phone
    })
  }
}

// 创建默认实例
export const externalAPI = new ExternalLotteryAPI() 