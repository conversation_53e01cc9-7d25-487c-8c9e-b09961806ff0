<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
      <p class="text-gray-600">管理系统用户和参与记录</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div
        v-for="stat in userStats"
        :key="stat.title"
        class="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
          </div>
          <div class="p-3 rounded-full" :class="stat.bgColor">
            <component :is="stat.icon" class="w-6 h-6" :class="stat.iconColor" />
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索用户</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="输入用户名或手机号"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
          <Select v-model="statusFilter" placeholder="全部状态">
            <SelectOption value="">全部状态</SelectOption>
            <SelectOption value="active">正常</SelectOption>
            <SelectOption value="banned">已封禁</SelectOption>
          </Select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">用户类型</label>
          <Select v-model="typeFilter" placeholder="全部类型">
            <SelectOption value="">全部类型</SelectOption>
            <SelectOption value="member">会员</SelectOption>
            <SelectOption value="regular">普通用户</SelectOption>
          </Select>
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 border-b border-gray-200">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中奖次数</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <User class="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                    <div class="text-sm text-gray-500">{{ user.phone }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="user.type === 'member' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'"
                >
                  {{ user.type === 'member' ? '会员' : '普通用户' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ user.status === 'active' ? '正常' : '已封禁' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ user.participations }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ user.wins }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ user.registerDate }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="viewUser(user.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    <Eye class="w-4 h-4" />
                  </button>
                  <button
                    @click="toggleUserStatus(user)"
                    :class="user.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                  >
                    <component :is="user.status === 'active' ? Ban : CheckCircle" class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          上一页
        </button>
        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg">{{ currentPage }}</span>
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 用户详情模态框 -->
    <div
      v-if="showUserModal"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click.self="closeUserModal"
    >
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">用户详情</h3>
            <button
              @click="closeUserModal"
              class="text-gray-400 hover:text-gray-600"
            >
              <X class="w-6 h-6" />
            </button>
          </div>

          <div v-if="selectedUser" class="space-y-6">
            <!-- 基本信息 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-3">基本信息</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">用户名:</span>
                  <span class="ml-2 text-gray-900">{{ selectedUser.username }}</span>
                </div>
                <div>
                  <span class="text-gray-500">手机号:</span>
                  <span class="ml-2 text-gray-900">{{ selectedUser.phone }}</span>
                </div>
                <div>
                  <span class="text-gray-500">用户类型:</span>
                  <span class="ml-2 text-gray-900">{{ selectedUser.type === 'member' ? '会员' : '普通用户' }}</span>
                </div>
                <div>
                  <span class="text-gray-500">注册时间:</span>
                  <span class="ml-2 text-gray-900">{{ selectedUser.registerDate }}</span>
                </div>
              </div>
            </div>

            <!-- 参与统计 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-3">参与统计</h4>
              <div class="grid grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">{{ selectedUser.participations }}</div>
                  <div class="text-sm text-gray-600">参与次数</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">{{ selectedUser.wins }}</div>
                  <div class="text-sm text-gray-600">中奖次数</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                  <div class="text-2xl font-bold text-yellow-600">
                    {{ selectedUser.participations > 0 ? Math.round(selectedUser.wins / selectedUser.participations * 100) : 0 }}%
                  </div>
                  <div class="text-sm text-gray-600">中奖率</div>
                </div>
              </div>
            </div>

            <!-- 最近参与记录 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-3">最近参与记录</h4>
              <div class="space-y-2 max-h-40 overflow-y-auto">
                <div
                  v-for="record in selectedUser.recentRecords"
                  :key="record.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ record.activity }}</div>
                    <div class="text-xs text-gray-500">{{ record.date }}</div>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    :class="record.result === 'win' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                  >
                    {{ record.result === 'win' ? '中奖' : '未中奖' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { User, Users, Award, Activity, Eye, X } from 'lucide-vue-next'
import { Select, SelectOption } from '@/components/ui'

const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showUserModal = ref(false)
const selectedUser = ref(null)

const userStats = [
  {
    title: '总用户数',
    value: '1,234',
    icon: Users,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    title: '活跃用户',
    value: '856',
    icon: Activity,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    title: '会员用户',
    value: '342',
    icon: Award,
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600'
  },
  {
    title: '今日新增',
    value: '23',
    icon: User,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  }
]

const users = ref([
  {
    id: 1,
    username: '张三',
    phone: '138****1234',
    type: 'member',
    status: 'active',
    participations: 15,
    wins: 3,
    registerDate: '2024-01-15',
    recentRecords: [
      { id: 1, activity: '春节大抽奖', date: '2024-01-20', result: 'win' },
      { id: 2, activity: '会员专享抽奖', date: '2024-01-19', result: 'lose' },
      { id: 3, activity: '新品发布抽奖', date: '2024-01-18', result: 'lose' }
    ]
  },
  {
    id: 2,
    username: '李四',
    phone: '139****5678',
    type: 'regular',
    status: 'active',
    participations: 8,
    wins: 1,
    registerDate: '2024-01-10',
    recentRecords: [
      { id: 4, activity: '春节大抽奖', date: '2024-01-20', result: 'lose' },
      { id: 5, activity: '新品发布抽奖', date: '2024-01-18', result: 'win' }
    ]
  },
  {
    id: 3,
    username: '王五',
    phone: '137****9012',
    type: 'member',
    status: 'banned',
    participations: 25,
    wins: 8,
    registerDate: '2024-01-05',
    recentRecords: []
  }
])

const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = user.username.includes(searchQuery.value) || user.phone.includes(searchQuery.value)
    const matchesStatus = !statusFilter.value || user.status === statusFilter.value
    const matchesType = !typeFilter.value || user.type === typeFilter.value
    return matchesSearch && matchesStatus && matchesType
  })
})

const totalItems = computed(() => filteredUsers.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  typeFilter.value = ''
}

const viewUser = (id: number) => {
  selectedUser.value = users.value.find(user => user.id === id)
  showUserModal.value = true
}

const closeUserModal = () => {
  showUserModal.value = false
  selectedUser.value = null
}

const toggleUserStatus = (user: any) => {
  const action = user.status === 'active' ? '封禁' : '解封'
  if (confirm(`确定要${action}用户 ${user.username} 吗？`)) {
    user.status = user.status === 'active' ? 'banned' : 'active'
  }
}
</script>