<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold tracking-tight">创建活动</h1>
      <p class="text-muted-foreground">创建和管理抽奖活动，设置奖品和规则</p>
    </div>

    <!-- 步骤指示器 -->
    <Card class="card-hover">
      <CardContent class="p-6">
        <div class="flex items-center justify-between mb-8">
          <template
            v-for="(step, index) in steps"
            :key="step.id"
          >
            <div class="flex items-center">
              <div class="flex items-center">
                <div
                  class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium"
                  :class="currentStep >= step.id ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'"
                >
                  {{ step.id }}
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium" :class="currentStep >= step.id ? 'text-primary' : 'text-muted-foreground'">
                    {{ step.title }}
                  </div>
                  <div class="text-xs text-muted-foreground">{{ step.description }}</div>
                </div>
              </div>
            </div>
            <div
              v-if="index < steps.length - 1"
              class="flex-1 h-0.5 mx-4"
              :class="currentStep > step.id ? 'bg-primary' : 'bg-muted'"
            ></div>
          </template>
        </div>
      </CardContent>
    </Card>

    <!-- 表单内容 -->
    <Card class="card-hover">
      <CardContent class="p-6">
        <!-- 步骤1: 基础设置 -->
        <div v-if="currentStep === 1" class="space-y-6">
          <div class="flex items-center mb-6">
            <Settings class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">基础设置</h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-foreground mb-2">
                <span class="text-destructive">*</span> 活动类型
              </label>
              <Select 
                v-model="formData.activityType" 
                placeholder="请选择活动类型"
                :display-value="getActivityTypeLabel(formData.activityType)"
              >
                <SelectOption value="qr">扫码抽奖</SelectOption>
              </Select>
            </div>

            <div>
              <label class="block text-sm font-medium text-foreground mb-2">
                <span class="text-destructive">*</span> 活动形式
              </label>
              <Select 
                v-model="formData.activityFormat" 
                placeholder="请选择活动形式"
                :display-value="getActivityFormatLabel(formData.activityFormat)"
              >
                <SelectOption value="wheel">转盘</SelectOption>
                <SelectOption value="grid">九宫格</SelectOption>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-foreground mb-2">
                <span class="text-destructive">*</span> 活动名称
              </label>
              <Input
                v-model="formData.activityName"
                type="text"
                placeholder="请输入活动名称"
                maxlength="50"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-foreground mb-2">
                <span class="text-destructive">*</span> 活动时间
              </label>
              <DateTimeRangePicker
                v-model="formData.activityTimeRange"
                placeholder="选择活动时间范围"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-foreground mb-2">
              <span class="text-destructive">*</span> 活动规则
            </label>
            <RichTextEditor
              v-model="formData.activityRules"
              placeholder="请输入活动规则说明，支持富文本编辑和图片上传"
              :max-length="2000"
            />
          </div>
        </div>

        <!-- 其他步骤内容可以在这里添加 -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div class="flex items-center mb-6">
            <Gift class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">奖励设置</h2>
          </div>
          <p class="text-muted-foreground">奖励设置功能开发中...</p>
        </div>

        <div v-if="currentStep === 3" class="space-y-6">
          <div class="flex items-center mb-6">
            <Target class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">抽奖设置</h2>
          </div>
          <p class="text-muted-foreground">抽奖设置功能开发中...</p>
        </div>

        <div v-if="currentStep === 4" class="space-y-6">
          <div class="flex items-center mb-6">
            <Palette class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">UI设置</h2>
          </div>
          <p class="text-muted-foreground">UI设置功能开发中...</p>
        </div>

        <div v-if="currentStep === 5" class="space-y-6">
          <div class="flex items-center mb-6">
            <MessageSquare class="w-5 h-5 text-muted-foreground mr-2" />
            <h2 class="text-lg font-semibold">提示语设置</h2>
          </div>
          <p class="text-muted-foreground">提示语设置功能开发中...</p>
        </div>

        <!-- 底部操作按钮 -->
        <div class="flex justify-between mt-8 pt-6 border-t">
          <Button variant="outline" @click="saveDraft">
            保存草稿
          </Button>
          <div class="flex space-x-3">
            <Button
              v-if="currentStep > 1"
              variant="outline"
              @click="prevStep"
            >
              上一步
            </Button>
            <Button
              v-if="currentStep < 5"
              @click="nextStep"
              class="btn-animate"
            >
              下一步
              <ChevronRight class="w-4 h-4 inline ml-1" />
            </Button>
            <Button
              v-if="currentStep === 5"
              @click="submitForm"
              class="bg-green-600 hover:bg-green-700 btn-animate"
            >
              创建活动
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Settings, Gift, Target, Palette, MessageSquare, 
  Plus, ChevronRight 
} from 'lucide-vue-next'
import DateTimeRangePicker from '@/components/ui/datetime-range-picker.vue'
import { Card, CardContent, Button, Input, Textarea, Select, SelectOption, RichTextEditor } from '@/components/ui'

const router = useRouter()

const currentStep = ref(1)

const steps = [
  { id: 1, title: '基础设置', description: '设置活动基本信息' },
  { id: 2, title: '奖励设置', description: '配置奖品和概率' },
  { id: 3, title: '抽奖设置', description: '设置抽奖规则' },
  { id: 4, title: 'UI设置', description: '上传活动图片' },
  { id: 5, title: '提示语设置', description: '设置中奖提示' }
]

const formData = ref({
  activityType: 'qr',
  activityFormat: 'wheel',
  activityName: '',
  activityTimeRange: {} as { startDateTime?: Date; endDateTime?: Date },
  activityRules: '',
})



const nextStep = () => {
  if (currentStep.value < 5) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const saveDraft = () => {
  console.log('保存草稿:', formData.value)
  alert('草稿已保存！')
}

const submitForm = () => {
  console.log('提交表单:', formData.value)
  alert('活动创建成功！')
  router.push('/activities')
}

const getActivityTypeLabel = (value: string) => {
  const labels = {
    'qr': '扫码抽奖'
  }
  return labels[value as keyof typeof labels] || value
}

const getActivityFormatLabel = (value: string) => {
  const labels = {
    'wheel': '转盘',
    'grid': '九宫格'
  }
  return labels[value as keyof typeof labels] || value
}


</script>
