<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">活动管理</h1>
        <p class="text-gray-600">管理所有抽奖活动</p>
      </div>
      <router-link
        to="/activities/create"
        class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        创建活动
      </router-link>
    </div>

    <!-- 筛选和搜索 -->
    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">活动时间</label>
            <DateTimeRangePicker
              v-model="dateTimeRangeFilter"
              placeholder="选择活动时间"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">活动名称/ID</label>
            <Input
              v-model="searchQuery"
              type="text"
              placeholder="输入活动名称或ID"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">活动状态</label>
            <Select v-model="statusFilter" placeholder="全部状态" class="w-full">
              <SelectOption value="">全部状态</SelectOption>
              <SelectOption value="进行中">进行中</SelectOption>
              <SelectOption value="待开始">待开始</SelectOption>
              <SelectOption value="已结束">已结束</SelectOption>
            </Select>
          </div>
          <div class="flex items-end space-x-2">
            <Button @click="searchActivities" class="flex-1">
              查询
            </Button>
            <Button @click="resetFilters" variant="outline" class="flex-1">
              重置
            </Button>
            <Button @click="exportActivities" variant="outline" class="flex-1">
              导出
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 活动列表 -->
    <Card>
      <div class="overflow-x-auto">
        <Table class="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead class="w-24">活动ID</TableHead>
              <TableHead class="w-32">活动类型</TableHead>
              <TableHead class="w-64">活动名称</TableHead>
              <TableHead class="w-72">活动时间</TableHead>
              <TableHead class="w-32">活动状态</TableHead>
              <TableHead class="w-24">创建人</TableHead>
              <TableHead class="w-48">创建时间</TableHead>
              <TableHead class="w-48">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="activity in filteredActivities" :key="activity.id" class="hover:bg-gray-50">
              <TableCell class="font-medium w-24">{{ activity.id }}</TableCell>
              <TableCell class="w-32">
                <Badge variant="secondary" class="text-sm">
                  {{ getTypeLabel(activity.type) }}
                </Badge>
              </TableCell>
              <TableCell class="w-64">
                <div>
                  <div class="font-medium text-gray-900">{{ activity.name }}</div>
                  <div class="text-sm text-gray-500">{{ activity.description }}</div>
                </div>
              </TableCell>
              <TableCell class="w-72">
                <div class="text-sm text-gray-900 whitespace-nowrap">
                  {{ activity.startDate }} 至 {{ activity.endDate }}
                </div>
              </TableCell>
              <TableCell class="w-32">
                <Badge :variant="getStatusVariant(activity.status)">
                  {{ getStatusLabel(activity.status) }}
                </Badge>
              </TableCell>
              <TableCell class="text-sm text-gray-900 w-24">{{ activity.creator }}</TableCell>
              <TableCell class="text-sm text-gray-500 w-48 whitespace-nowrap">{{ activity.createTime }}</TableCell>
              <TableCell class="w-48">
                <div class="flex items-center space-x-2">
                  <button
                    @click="disableActivity(activity.id)"
                    class="text-red-600 hover:text-red-900 hover:bg-red-50 px-3 py-1 text-sm font-medium transition-colors"
                  >
                    停用
                  </button>
                  <Button
                    @click="viewActivity(activity.id)"
                    variant="ghost"
                    size="sm"
                    class="text-gray-700 hover:text-gray-900 hover:bg-gray-50 px-3 py-1 text-sm"
                  >
                    查看
                  </Button>
                  <Button
                    @click="editActivity(activity.id)"
                    variant="ghost"
                    size="sm"
                    class="text-green-600 hover:text-green-900 hover:bg-green-50 px-3 py-1 text-sm"
                  >
                    编辑
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </Card>

    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="flex items-center space-x-2">
        <Button
          @click="currentPage--"
          :disabled="currentPage === 1"
          variant="outline"
          size="sm"
        >
          上一页
        </Button>
        <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg">{{ currentPage }}</span>
        <Button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          variant="outline"
          size="sm"
        >
          下一页
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus } from 'lucide-vue-next'
import { 
  Button, 
  Badge, 
  Card, 
  CardContent, 
  Input, 
  Select, 
  SelectOption,
  Table, 
  TableHeader, 
  TableBody, 
  TableRow, 
  TableHead, 
  TableCell,
  DateTimeRangePicker
} from '@/components/ui'

const router = useRouter()

const searchQuery = ref('')
const statusFilter = ref('')
const dateTimeRangeFilter = ref<{ startDateTime?: Date; endDateTime?: Date }>({})
const currentPage = ref(1)
const pageSize = ref(10)

const activities = ref([
  {
    id: 1,
    name: '春节大抽奖',
    description: '新春佳节，好礼相送',
    type: 'qr',
    status: 'active',
    participants: 1234,
    startDate: '2024-01-15',
    endDate: '2024-02-15',
    creator: '张三',
    createTime: '2024-01-10 10:30:00'
  },
  {
    id: 2,
    name: '会员专享抽奖',
    description: '会员福利，限时参与',
    type: 'qr',
    status: 'pending',
    participants: 567,
    startDate: '2024-02-01',
    endDate: '2024-02-28',
    creator: '李四',
    createTime: '2024-01-25 14:20:00'
  },
  {
    id: 3,
    name: '新品发布抽奖',
    description: '新品上市，抽奖送礼',
    type: 'qr',
    status: 'ended',
    participants: 890,
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    creator: '王五',
    createTime: '2023-12-20 09:15:00'
  },
  {
    id: 4,
    name: '测试活动',
    description: '测试用活动',
    type: 'qr',
    status: 'pending',
    participants: 0,
    startDate: '2024-03-01',
    endDate: '2024-03-31',
    creator: '赵六',
    createTime: '2024-02-15 16:45:00'
  }
])

const filteredActivities = computed(() => {
  return activities.value.filter(activity => {
    const matchesSearch = activity.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         activity.id.toString().includes(searchQuery.value)
    const matchesStatus = !statusFilter.value || getStatusLabel(activity.status) === statusFilter.value
    
    // 时间范围筛选
    let matchesDateRange = true
    if (dateTimeRangeFilter.value.startDateTime) {
      const startDate = new Date(activity.startDate)
      if (startDate < dateTimeRangeFilter.value.startDateTime) {
        matchesDateRange = false
      }
    }
    if (dateTimeRangeFilter.value.endDateTime) {
      const endDate = new Date(activity.endDate)
      if (endDate > dateTimeRangeFilter.value.endDateTime) {
        matchesDateRange = false
      }
    }
    
    return matchesSearch && matchesStatus && matchesDateRange
  })
})

const totalItems = computed(() => filteredActivities.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

const getTypeLabel = (type: string) => {
  const labels = {
    qr: '扫码抽奖'
  }
  return labels[type as keyof typeof labels] || type
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '进行中',
    pending: '待开始',
    ended: '已结束',
    disabled: '已停用'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
  const variants = {
    active: 'default',
    pending: 'secondary',
    ended: 'outline',
    disabled: 'destructive'
  }
  return (variants[status as keyof typeof variants] as 'default' | 'secondary' | 'destructive' | 'outline') || 'outline'
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  dateTimeRangeFilter.value = {}
}

const searchActivities = () => {
  // 执行查询逻辑
  console.log('执行查询:', {
    searchQuery: searchQuery.value,
    statusFilter: statusFilter.value,
    dateTimeRangeFilter: {
      startDateTime: dateTimeRangeFilter.value.startDateTime?.toISOString(),
      endDateTime: dateTimeRangeFilter.value.endDateTime?.toISOString()
    }
  })
}

const exportActivities = () => {
  // 导出活动数据
  console.log('导出活动数据')
}

const editActivity = (id: number) => {
  router.push(`/activities/edit/${id}`)
}

const viewActivity = (id: number) => {
  // 查看活动详情
  console.log('查看活动:', id)
}

const disableActivity = (id: number) => {
  if (confirm('确定要停用这个活动吗？')) {
    const activity = activities.value.find(a => a.id === id)
    if (activity) {
      activity.status = 'disabled'
      console.log('活动已停用:', id)
    }
  }
}


</script>