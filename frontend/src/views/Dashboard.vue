<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-3xl font-bold text-gray-900">数据概览</h1>
      <p class="text-gray-600">查看系统整体运营数据</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="stat in stats"
        :key="stat.title"
        class="bg-white border border-gray-200 rounded-lg shadow-sm p-6 card-hover"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
            <p class="text-sm" :class="stat.change >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ stat.change >= 0 ? '+' : '' }}{{ stat.change }}%
            </p>
          </div>
          <div class="p-3 rounded-full" :class="stat.bgColor">
            <component :is="stat.icon" class="w-6 h-6" :class="stat.iconColor" />
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 活动参与趋势 -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm card-hover">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">活动参与趋势</h3>
        </div>
        <div class="p-6">
          <div class="h-64 flex items-center justify-center text-gray-500">
            <TrendingUp class="w-8 h-8 mr-2" />
            图表区域 (可集成 Chart.js 或其他图表库)
          </div>
        </div>
      </div>

      <!-- 奖品分布 -->
      <div class="bg-white border border-gray-200 rounded-lg shadow-sm card-hover">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">奖品分布</h3>
        </div>
        <div class="p-6">
          <div class="h-64 flex items-center justify-center text-gray-500">
            <PieChart class="w-8 h-8 mr-2" />
            图表区域 (可集成 Chart.js 或其他图表库)
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="bg-white border border-gray-200 rounded-lg shadow-sm card-hover">
      <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">最近活动</h3>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Gift class="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h4 class="font-medium text-gray-900">{{ activity.name }}</h4>
                <p class="text-sm text-gray-600">{{ activity.description }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-900">{{ activity.participants }} 人参与</p>
              <p class="text-sm text-gray-600">{{ activity.date }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Gift, Users, Award, Activity, TrendingUp, PieChart } from 'lucide-vue-next'

const stats = [
  {
    title: '总活动数',
    value: '24',
    change: 12,
    icon: Gift,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    title: '总用户数',
    value: '1,234',
    change: 8,
    icon: Users,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    title: '总奖品数',
    value: '156',
    change: -2,
    icon: Award,
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600'
  },
  {
    title: '今日参与',
    value: '89',
    change: 15,
    icon: Activity,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  }
]

const recentActivities = [
  {
    id: 1,
    name: '春节大抽奖',
    description: '新春佳节，好礼相送',
    participants: 1234,
    date: '2024-01-15'
  },
  {
    id: 2,
    name: '会员专享抽奖',
    description: '会员福利，限时参与',
    participants: 567,
    date: '2024-01-14'
  },
  {
    id: 3,
    name: '新品发布抽奖',
    description: '新品上市，抽奖送礼',
    participants: 890,
    date: '2024-01-13'
  }
]
</script>