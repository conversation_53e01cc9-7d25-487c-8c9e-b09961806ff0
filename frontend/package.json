{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@vueuse/core": "^13.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-vue-next": "^0.344.0", "radix-vue": "^1.9.17", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.1", "vue": "^3.4.38", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}