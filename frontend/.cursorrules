# Cursor AI 开发规则 - 抽奖管理系统前端项目

## 项目技术栈
- Vue 3.4.38 (Composition API)
- TypeScript 5.5.3 (严格类型检查)
- Vite 5.4.2
- Vue Router 4.3.0
- Tailwind CSS 3.4.0
- Lucide Vue Next 0.344.0

## 代码开发规范

### Vue 组件开发
- 使用 `<script setup lang="ts">` 语法
- 组件文件使用 PascalCase 命名 (如: UserProfile.vue)
- 组件名使用 PascalCase
- 变量名使用 camelCase
- 常量名使用 UPPER_SNAKE_CASE
- 方法名使用 camelCase

### TypeScript 规范
- 避免使用 `any` 类型
- 为所有变量提供明确的类型定义
- 使用泛型提高代码复用性
- 优先使用接口而非类型别名
- 严格类型检查

### 组件结构模板
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 导入语句
import { ref, computed } from 'vue'

// 类型定义
interface Props {
  title: string
  count?: number
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// 响应式数据
const loading = ref(false)

// 计算属性
const displayTitle = computed(() => `${props.title} (${props.count})`)

// 方法定义
const handleClick = () => {
  // 处理逻辑
}
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 样式规范
- 优先使用 Tailwind CSS 类名
- 避免内联样式
- 移动优先的响应式设计
- 自定义样式放在 `<style scoped>` 块中

### 路由规范
- 路由懒加载：component: () => import('../views/Component.vue')
- 路由元信息包含 title 和权限要求
- 实现路由守卫进行权限验证

### API 调用规范
- 使用封装的 request 工具
- 统一的错误处理机制
- 类型安全的 API 调用
- 请求/响应拦截器

### 文件组织规范
```
src/
├── components/          # 公共组件
├── views/              # 页面组件
├── router/             # 路由配置
├── utils/              # 工具函数
├── api/                # API 接口
├── stores/             # 状态管理
├── types/              # 类型定义
├── config/             # 配置文件
└── assets/             # 静态资源
```

### 性能优化要求
- 路由级别的代码分割
- 组件懒加载
- 使用 v-memo 优化列表渲染
- 计算属性缓存

### 安全规范
- 输入验证和净化
- XSS 防护（避免直接使用 v-html）
- 环境变量管理敏感信息
- CSRF 防护

### 代码质量要求
- TypeScript 类型检查通过
- ESLint 检查通过
- 组件命名符合规范
- 代码注释完整
- 错误处理完善
- 响应式设计支持

## AI 处理指导原则

1. 始终使用 TypeScript：为所有变量、函数、组件提供类型定义
2. 遵循 Vue 3 Composition API：使用 <script setup> 语法
3. 优先使用 Tailwind CSS：避免自定义 CSS，使用工具类
4. 组件单一职责：每个组件只负责一个功能
5. 类型安全：避免使用 any 类型，提供完整的类型定义
6. 错误处理：为所有异步操作提供错误处理
7. 响应式设计：确保组件在不同屏幕尺寸下正常工作
8. 性能优化：使用懒加载、缓存等优化技术
9. 代码复用：提取公共逻辑到工具函数或组合式函数
10. 文档注释：为复杂逻辑添加清晰的注释

## 禁止事项
- 不使用 any 类型
- 不直接使用 v-html 渲染用户输入
- 不在代码中硬编码敏感信息
- 不忽略错误处理
- 不使用内联样式

## 示例代码风格

### 组件示例
```vue
<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ title }}</h2>
    <div class="space-y-4">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

defineProps<Props>()
</script>
```

### API 调用示例
```typescript
import { request } from '@/utils/request'
import type { User } from '@/types/user'

export const userApi = {
  getUsers: async (): Promise<User[]> => {
    try {
      const response = await request.get<ApiResponse<User[]>>('/users')
      return response.data
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  }
}
```

### 类型定义示例
```typescript
export interface User {
  id: number
  name: string
  email: string
  status: UserStatus
  createdAt: string
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}
```

遵循这些规则确保代码质量和一致性。 